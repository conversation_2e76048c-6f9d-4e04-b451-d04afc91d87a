# Generated by Django 5.2.4 on 2025-07-27 01:27

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('landmarket', '0002_land_inquiry_landimage_favorite'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SavedSearch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name for this saved search', max_length=100)),
                ('search_query', models.CharField(blank=True, help_text='Search keywords', max_length=200)),
                ('location_filter', models.CharField(blank=True, help_text='Location filter', max_length=200)),
                ('property_type_filter', models.Char<PERSON>ield(blank=True, choices=[('residential', 'Residential'), ('commercial', 'Commercial'), ('agricultural', 'Agricultural'), ('recreational', 'Recreational')], help_text='Property type filter', max_length=20)),
                ('min_price', models.DecimalField(blank=True, decimal_places=2, help_text='Minimum price', max_digits=12, null=True)),
                ('max_price', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum price', max_digits=12, null=True)),
                ('min_size', models.DecimalField(blank=True, decimal_places=2, help_text='Minimum size in acres', max_digits=10, null=True)),
                ('max_size', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum size in acres', max_digits=10, null=True)),
                ('email_alerts', models.BooleanField(default=True, help_text='Send email alerts for new matching properties')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this search is active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_alert_sent', models.DateTimeField(blank=True, help_text='When the last alert was sent', null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='saved_searches', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Saved Search',
                'verbose_name_plural': 'Saved Searches',
                'ordering': ['-created_at'],
            },
        ),
    ]
