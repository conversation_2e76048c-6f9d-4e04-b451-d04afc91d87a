{% extends 'dashboards/base_dashboard.html' %}

{% block title %}Admin Dashboard - LandHub{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()" class="space-y-2">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='/admin-dashboard/' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Dashboard' description='Overview & insights' item_key='overview' %}

    <!-- User Management -->
    {% include 'components/sidebar_nav_item.html' with href='/dashboard/users/' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path></svg>' label='Users' description='Manage user accounts' item_key='users' %}

    <!-- Listing Approval -->
    {% include 'components/sidebar_nav_item.html' with href='/dashboard/listings/' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>' label='Listings' description='Review & approve' item_key='listings' badge_count=pending_listings %}

    <!-- Analytics & Reports -->
    {% include 'components/sidebar_nav_item.html' with href='/dashboard/analytics/' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Analytics' description='Reports & insights' item_key='reports' %}

    <!-- Divider -->
    <div class="my-6">
        <div class="h-px bg-gradient-to-r from-transparent via-slate-600 to-transparent"></div>
    </div>

    <!-- Settings -->
    {% include 'components/sidebar_nav_item.html' with href='/dashboard/settings/' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>' label='Settings' description='System configuration' item_key='settings' %}

    <!-- Support -->
    {% include 'components/sidebar_nav_item.html' with href='/dashboard/support/' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>' label='Support' description='Help & documentation' item_key='support' %}
</div>
{% endblock %}

{% block page_description %}Manage users, approve listings, and monitor platform performance{% endblock %}

{% block page_actions %}
<button class="btn btn-secondary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
    </svg>
    Export Report
</button>
<button class="btn btn-primary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>
    Quick Action
</button>
{% endblock %}

{% block dashboard_content %}
<!-- Quick Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Users -->
    <div class="group relative bg-white rounded-2xl shadow-sm border border-secondary-100 hover:shadow-xl hover:border-blue-200 transition-all duration-300 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        <div class="relative p-6">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                        <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                    </div>
                    <p class="text-sm font-medium text-secondary-600 mb-1">Total Users</p>
                    <p class="text-3xl font-bold text-secondary-900 mb-2">{{ total_users|default:"1,234" }}</p>
                    <div class="flex items-center space-x-1">
                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-xs font-medium text-green-600">+12% from last month</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Listings -->
    <div class="group relative bg-white rounded-2xl shadow-sm border border-secondary-100 hover:shadow-xl hover:border-green-200 transition-all duration-300 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-green-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        <div class="relative p-6">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    </div>
                    <p class="text-sm font-medium text-secondary-600 mb-1">Active Listings</p>
                    <p class="text-3xl font-bold text-secondary-900 mb-2">{{ active_listings|default:"567" }}</p>
                    <div class="flex items-center space-x-1">
                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-xs font-medium text-green-600">+8% from last month</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reports -->
    <div class="group relative bg-white rounded-2xl shadow-sm border border-secondary-100 hover:shadow-xl hover:border-purple-200 transition-all duration-300 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-purple-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        <div class="relative p-6">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                    </div>
                    <p class="text-sm font-medium text-secondary-600 mb-1">Reports</p>
                    <p class="text-3xl font-bold text-secondary-900 mb-2">{{ total_reports|default:"89" }}</p>
                    <div class="flex items-center space-x-1">
                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-xs font-medium text-green-600">+15% from last month</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Approval -->
    <div class="group relative bg-white rounded-2xl shadow-sm border border-secondary-100 hover:shadow-xl hover:border-amber-200 transition-all duration-300 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-amber-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        <div class="relative p-6">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="w-2 h-2 bg-amber-400 rounded-full animate-pulse"></div>
                    </div>
                    <p class="text-sm font-medium text-secondary-600 mb-1">Pending Approval</p>
                    <p class="text-3xl font-bold text-secondary-900 mb-2">{{ pending_listings|default:"23" }}</p>
                    <div class="flex items-center space-x-1">
                        <svg class="w-4 h-4 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-xs font-medium text-amber-600">Requires attention</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Recent Activity -->
    <div class="lg:col-span-2">
        <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
            <div class="px-6 py-5 border-b border-secondary-100 bg-gradient-to-r from-secondary-50/50 to-transparent">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-xl font-bold text-secondary-900">Recent Activities</h3>
                        <p class="text-sm text-secondary-600 mt-1">Latest platform activities and user actions</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-xs font-medium text-secondary-500">Live</span>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <!-- Activity Table Header -->
                    <div class="grid grid-cols-12 gap-4 pb-3 border-b border-secondary-100">
                        <div class="col-span-4">
                            <span class="text-xs font-semibold text-secondary-500 uppercase tracking-wider">User</span>
                        </div>
                        <div class="col-span-4">
                            <span class="text-xs font-semibold text-secondary-500 uppercase tracking-wider">Action</span>
                        </div>
                        <div class="col-span-4">
                            <span class="text-xs font-semibold text-secondary-500 uppercase tracking-wider">Date</span>
                        </div>
                    </div>

                    <!-- Activity Items -->
                    <div class="space-y-3">
                        <div class="grid grid-cols-12 gap-4 items-center p-3 rounded-xl hover:bg-secondary-50 transition-colors duration-200 group">
                            <div class="col-span-4 flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-sm">
                                    <span class="text-white font-semibold text-sm">SC</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-secondary-900">Sophia Clark</p>
                                    <p class="text-xs text-secondary-500"><EMAIL></p>
                                </div>
                            </div>
                            <div class="col-span-4">
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6z"></path>
                                        </svg>
                                    </div>
                                    <span class="text-sm text-secondary-700">User signed up</span>
                                </div>
                            </div>
                            <div class="col-span-4">
                                <span class="text-sm text-secondary-500">2024-07-26</span>
                            </div>
                        </div>

                        <div class="grid grid-cols-12 gap-4 items-center p-3 rounded-xl hover:bg-secondary-50 transition-colors duration-200 group">
                            <div class="col-span-4 flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center shadow-sm">
                                    <span class="text-white font-semibold text-sm">EC</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-secondary-900">Ethan Carter</p>
                                    <p class="text-xs text-secondary-500"><EMAIL></p>
                                </div>
                            </div>
                            <div class="col-span-4">
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-green-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-sm text-secondary-700">Listing approved</span>
                                </div>
                            </div>
                            <div class="col-span-4">
                                <span class="text-sm text-secondary-500">2024-07-25</span>
                            </div>
                        </div>

                        <div class="grid grid-cols-12 gap-4 items-center p-3 rounded-xl hover:bg-secondary-50 transition-colors duration-200 group">
                            <div class="col-span-4 flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-amber-500 to-amber-600 rounded-full flex items-center justify-center shadow-sm">
                                    <span class="text-white font-semibold text-sm">OB</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-secondary-900">Olivia Bennett</p>
                                    <p class="text-xs text-secondary-500"><EMAIL></p>
                                </div>
                            </div>
                            <div class="col-span-4">
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-orange-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-3 h-3 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-sm text-secondary-700">Report submitted</span>
                                </div>
                            </div>
                            <div class="col-span-4">
                                <span class="text-sm text-secondary-500">2024-07-24</span>
                            </div>
                        </div>

                        <div class="grid grid-cols-12 gap-4 items-center p-3 rounded-xl hover:bg-secondary-50 transition-colors duration-200 group">
                            <div class="col-span-4 flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center shadow-sm">
                                    <span class="text-white font-semibold text-sm">LH</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-secondary-900">Liam Harper</p>
                                    <p class="text-xs text-secondary-500"><EMAIL></p>
                                </div>
                            </div>
                            <div class="col-span-4">
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-sm text-secondary-700">User updated profile</span>
                                </div>
                            </div>
                            <div class="col-span-4">
                                <span class="text-sm text-secondary-500">2024-07-23</span>
                            </div>
                        </div>

                        <div class="grid grid-cols-12 gap-4 items-center p-3 rounded-xl hover:bg-secondary-50 transition-colors duration-200 group">
                            <div class="col-span-4 flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-teal-500 to-teal-600 rounded-full flex items-center justify-center shadow-sm">
                                    <span class="text-white font-semibold text-sm">AF</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-secondary-900">Ava Foster</p>
                                    <p class="text-xs text-secondary-500"><EMAIL></p>
                                </div>
                            </div>
                            <div class="col-span-4">
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-green-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-sm text-secondary-700">Listing created</span>
                                </div>
                            </div>
                            <div class="col-span-4">
                                <span class="text-sm text-secondary-500">2024-07-22</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div>
        <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
            <div class="px-6 py-5 border-b border-secondary-100 bg-gradient-to-r from-secondary-50/50 to-transparent">
                <h3 class="text-xl font-bold text-secondary-900">Quick Actions</h3>
                <p class="text-sm text-secondary-600 mt-1">Common administrative tasks</p>
            </div>
            <div class="p-6 space-y-4">
                <!-- Approve Listings Action -->
                <a href="{% url 'admin_listing_approval' %}?status=pending"
                   class="group relative w-full flex items-center p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl shadow-lg hover:shadow-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative flex items-center w-full">
                        <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-white">Approve Listings</h4>
                            <p class="text-blue-100 text-sm">Review pending property listings</p>
                        </div>
                        <div class="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                    </div>
                </a>

                <!-- View Reports Action -->
                <a href="{% url 'admin_analytics' %}"
                   class="group relative w-full flex items-center p-4 bg-white border-2 border-secondary-200 text-secondary-700 rounded-xl shadow-sm hover:shadow-lg hover:border-secondary-300 hover:bg-secondary-50 transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98]">
                    <div class="flex items-center w-full">
                        <div class="w-10 h-10 bg-secondary-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-secondary-200 group-hover:scale-110 transition-all duration-300">
                            <svg class="w-5 h-5 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-secondary-900">View Reports</h4>
                            <p class="text-secondary-600 text-sm">Analytics and performance data</p>
                        </div>
                        <div class="w-6 h-6 bg-secondary-100 rounded-full flex items-center justify-center group-hover:bg-secondary-200 transition-colors duration-300">
                            <svg class="w-3 h-3 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                    </div>
                </a>

                <!-- Manage Users Action -->
                <a href="{% url 'admin_user_management' %}"
                   class="group relative w-full flex items-center p-4 bg-white border-2 border-secondary-200 text-secondary-700 rounded-xl shadow-sm hover:shadow-lg hover:border-secondary-300 hover:bg-secondary-50 transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98]">
                    <div class="flex items-center w-full">
                        <div class="w-10 h-10 bg-secondary-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-secondary-200 group-hover:scale-110 transition-all duration-300">
                            <svg class="w-5 h-5 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-secondary-900">Manage Users</h4>
                            <p class="text-secondary-600 text-sm">User accounts and permissions</p>
                        </div>
                        <div class="w-6 h-6 bg-secondary-100 rounded-full flex items-center justify-center group-hover:bg-secondary-200 transition-colors duration-300">
                            <svg class="w-3 h-3 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

