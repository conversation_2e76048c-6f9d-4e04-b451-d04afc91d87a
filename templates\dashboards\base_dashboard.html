<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Dashboard - LandHub{% endblock %}</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>

    <!-- Unpoly -->
    <script src="https://unpkg.com/unpoly@3.7.2/unpoly.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/unpoly@3.7.2/unpoly.min.css">

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                            950: '#082f49'
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a'
                        },
                        accent: {
                            50: '#fefce8',
                            100: '#fef9c3',
                            200: '#fef08a',
                            300: '#fde047',
                            400: '#facc15',
                            500: '#eab308',
                            600: '#ca8a04',
                            700: '#a16207',
                            800: '#854d0e',
                            900: '#713f12'
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'display': ['Poppins', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'slide-down': 'slideDown 0.3s ease-out',
                        'scale-in': 'scaleIn 0.2s ease-out',
                        'bounce-subtle': 'bounceSubtle 0.6s ease-in-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        slideDown: {
                            '0%': { opacity: '0', transform: 'translateY(-10px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        scaleIn: {
                            '0%': { opacity: '0', transform: 'scale(0.95)' },
                            '100%': { opacity: '1', transform: 'scale(1)' }
                        },
                        bounceSubtle: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-5px)' }
                        }
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        [x-cloak] { display: none !important; }

        /* Base styles */
        body {
            font-family: 'Inter', system-ui, sans-serif;
            font-feature-settings: 'cv11', 'ss01';
            font-variation-settings: 'opsz' 32;
        }

        .font-display {
            font-family: 'Poppins', system-ui, sans-serif;
        }

        /* Enhanced button styles */
        .btn {
            @apply inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
        }

        .btn-primary {
            @apply btn bg-primary-600 hover:bg-primary-700 active:bg-primary-800 text-white shadow-sm hover:shadow-md focus:ring-primary-500;
        }

        .btn-secondary {
            @apply btn bg-white hover:bg-secondary-50 active:bg-secondary-100 text-secondary-900 border border-secondary-300 hover:border-secondary-400 shadow-sm hover:shadow-md focus:ring-secondary-500;
        }

        .btn-danger {
            @apply btn bg-red-600 hover:bg-red-700 active:bg-red-800 text-white shadow-sm hover:shadow-md focus:ring-red-500;
        }

        .btn-ghost {
            @apply btn bg-transparent hover:bg-secondary-100 active:bg-secondary-200 text-secondary-700 hover:text-secondary-900 focus:ring-secondary-500;
        }

        .btn-sm {
            @apply px-3 py-1.5 text-sm rounded-md;
        }

        .btn-md {
            @apply px-4 py-2 text-sm rounded-lg;
        }

        .btn-lg {
            @apply px-6 py-3 text-base rounded-lg;
        }

        /* Enhanced form styles */
        .form-input {
            @apply w-full px-4 py-3 border border-secondary-300 rounded-lg shadow-sm placeholder-secondary-400 bg-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-secondary-400;
        }

        .form-textarea {
            @apply form-input resize-vertical min-h-[100px];
        }

        .form-select {
            @apply form-input cursor-pointer;
        }

        .form-label {
            @apply block text-sm font-medium text-secondary-700 mb-2;
        }

        .form-error {
            @apply mt-1 text-sm text-red-600;
        }

        /* Enhanced card styles */
        .card {
            @apply bg-white rounded-xl shadow-sm border border-secondary-200 overflow-hidden transition-all duration-200 hover:shadow-md;
        }

        .card-elevated {
            @apply shadow-lg hover:shadow-xl;
        }

        .card-header {
            @apply px-6 py-5 border-b border-secondary-200 bg-secondary-50/50;
        }

        .card-body {
            @apply px-6 py-5;
        }

        .card-footer {
            @apply px-6 py-4 border-t border-secondary-200 bg-secondary-50/30;
        }

        /* Navigation enhancements */
        .nav-link {
            @apply relative px-3 py-2 text-sm font-medium transition-all duration-200 rounded-lg;
        }

        .nav-link-active {
            @apply bg-primary-100 text-primary-700;
        }

        .nav-link-inactive {
            @apply text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100;
        }

        /* Enhanced sidebar navigation styles */
        .sidebar-nav-active {
            @apply relative;
        }

        .sidebar-nav-inactive {
            @apply relative;
        }

        /* Custom scrollbar for sidebar */
        .sidebar-scroll::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar-scroll::-webkit-scrollbar-track {
            background: rgba(51, 65, 85, 0.3);
        }

        .sidebar-scroll::-webkit-scrollbar-thumb {
            background: rgba(148, 163, 184, 0.5);
            border-radius: 2px;
        }

        .sidebar-scroll::-webkit-scrollbar-thumb:hover {
            background: rgba(148, 163, 184, 0.7);
        }

        /* Glassmorphism effects */
        .glass-dark {
            @apply bg-slate-800/30 backdrop-blur-md border border-slate-700/50;
        }

        .glass-light {
            @apply bg-white/10 backdrop-blur-md border border-white/20;
        }

        /* Mobile navigation enhancements */
        #mobile-nav-content {
            min-height: 200px;
        }

        #mobile-nav-content:empty::before {
            content: "Loading navigation...";
            @apply text-slate-400 text-sm px-4 py-8 block text-center;
        }

        /* Mobile sidebar specific styles */
        @media (max-width: 1023px) {
            .sidebar-nav-active,
            .sidebar-nav-inactive {
                @apply w-full;
            }
        }

        /* Utility classes */
        .glass {
            @apply bg-white/80 backdrop-blur-sm border border-white/20;
        }

        .gradient-primary {
            background: linear-gradient(135deg, theme('colors.primary.600') 0%, theme('colors.primary.700') 100%);
        }

        .gradient-secondary {
            background: linear-gradient(135deg, theme('colors.secondary.100') 0%, theme('colors.secondary.200') 100%);
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Loading states */
        .loading {
            @apply opacity-50 pointer-events-none;
        }

        .spinner {
            @apply animate-spin rounded-full border-2 border-secondary-300 border-t-primary-600;
        }
    </style>

    {% block extra_head %}{% endblock %}
</head>
<body class="bg-secondary-50 font-sans antialiased">
    <!-- Full-page Dashboard Layout -->
    <div class="flex h-screen bg-secondary-50" x-data="dashboardApp()">
        <!-- Desktop Sidebar -->
        <div class="hidden lg:flex lg:w-72 lg:flex-col lg:fixed lg:inset-y-0 lg:z-30">
            <div class="flex flex-col flex-grow bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 shadow-2xl overflow-y-auto sidebar-scroll">
                <!-- Sidebar Header -->
                <div class="relative flex items-center justify-between flex-shrink-0 px-6 py-6 border-b border-slate-700/50">
                    <!-- Background Pattern -->
                    <div class="absolute inset-0 bg-gradient-to-r from-primary-600/10 to-purple-600/10"></div>
                    <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>

                    <div class="relative flex items-center space-x-4 w-full">
                        <div class="relative">
                            <div class="w-12 h-12 bg-gradient-to-br from-primary-500 via-primary-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg ring-2 ring-white/20 backdrop-blur-sm">
                                <span x-html="currentSectionIcon" class="w-6 h-6 text-white drop-shadow-sm"></span>
                            </div>
                            <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-slate-900 animate-pulse"></div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h2 class="text-lg font-display font-bold text-white truncate" x-text="currentSectionTitle"></h2>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                <p class="text-xs text-slate-300 capitalize font-medium">{{ user.profile.get_role_display }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="flex-1 px-6 py-8 space-y-3">
                    <!-- Navigation Section Header -->
                    <div class="mb-6">
                        <h3 class="text-xs font-semibold text-slate-400 uppercase tracking-wider mb-3 px-3">Navigation</h3>
                        <div class="h-px bg-gradient-to-r from-slate-700 via-slate-600 to-slate-700"></div>
                    </div>

                    {% block sidebar_navigation %}
                    <!-- Role-specific navigation will be defined in child templates -->
                    {% endblock %}

                    <!-- Quick Stats Section -->
                    <div class="mt-8 pt-6 border-t border-slate-700/50">
                        <h3 class="text-xs font-semibold text-slate-400 uppercase tracking-wider mb-4 px-3">Quick Stats</h3>
                        <div class="space-y-3">
                            <div class="bg-slate-800/50 rounded-xl p-4 border border-slate-700/30 backdrop-blur-sm">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-xs text-slate-400 font-medium">Active Users</p>
                                        <p class="text-lg font-bold text-white">1,234</p>
                                    </div>
                                    <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
                                        <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-slate-800/50 rounded-xl p-4 border border-slate-700/30 backdrop-blur-sm">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-xs text-slate-400 font-medium">Pending</p>
                                        <p class="text-lg font-bold text-amber-400">23</p>
                                    </div>
                                    <div class="w-8 h-8 bg-amber-500/20 rounded-lg flex items-center justify-center">
                                        <div class="w-3 h-3 bg-amber-400 rounded-full animate-pulse"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>

                <!-- Sidebar Footer -->
                <div class="flex-shrink-0 border-t border-slate-700/50 p-6 bg-slate-900/50 backdrop-blur-sm">
                    <!-- User Profile Section -->
                    <div class="relative">
                        <!-- Background Glow -->
                        <div class="absolute inset-0 bg-gradient-to-r from-primary-600/10 to-purple-600/10 rounded-xl blur-xl"></div>

                        <div class="relative bg-slate-800/50 rounded-xl p-4 border border-slate-700/30 backdrop-blur-sm">
                            <div class="flex items-center space-x-3">
                                {% if user.profile.avatar %}
                                    <div class="relative">
                                        <img src="{{ user.profile.avatar.url }}" alt="Avatar" class="w-10 h-10 rounded-full object-cover ring-2 ring-primary-500/50">
                                        <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-slate-800"></div>
                                    </div>
                                {% else %}
                                    <div class="relative">
                                        <div class="w-10 h-10 rounded-full bg-gradient-to-br from-primary-500 to-purple-600 flex items-center justify-center ring-2 ring-primary-500/50 shadow-lg">
                                            <span class="text-white font-bold text-sm">{{ user.first_name.0|default:user.username.0|upper }}</span>
                                        </div>
                                        <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-slate-800 animate-pulse"></div>
                                    </div>
                                {% endif %}
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-semibold text-white truncate">{{ user.get_full_name|default:user.username }}</p>
                                    <p class="text-xs text-slate-400 truncate">{{ user.email }}</p>
                                </div>
                                <div class="flex-shrink-0">
                                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions in Sidebar Footer -->
                    <div class="mt-4 space-y-2">
                        <a href="{% url 'auth:profile' %}"
                           class="group flex items-center px-4 py-3 text-sm text-slate-300 hover:text-white hover:bg-slate-800/50 rounded-xl transition-all duration-200 border border-transparent hover:border-slate-700/50">
                            <div class="w-8 h-8 bg-slate-700/50 rounded-lg flex items-center justify-center mr-3 group-hover:bg-primary-600/20 transition-colors duration-200">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <span class="font-medium">Profile Settings</span>
                            <svg class="w-4 h-4 ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>

                        <a href="{% url 'auth:logout' %}"
                           class="group flex items-center px-4 py-3 text-sm text-slate-300 hover:text-red-400 hover:bg-red-500/10 rounded-xl transition-all duration-200 border border-transparent hover:border-red-500/20">
                            <div class="w-8 h-8 bg-slate-700/50 rounded-lg flex items-center justify-center mr-3 group-hover:bg-red-500/20 transition-colors duration-200">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                            </div>
                            <span class="font-medium">Sign Out</span>
                            <svg class="w-4 h-4 ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div x-show="sidebarOpen"
             x-cloak
             class="fixed inset-0 flex z-40 lg:hidden"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0">

            <!-- Backdrop -->
            <div class="fixed inset-0 bg-secondary-600 bg-opacity-75"
                 @click="sidebarOpen = false"
                 aria-hidden="true"></div>

            <!-- Mobile Sidebar -->
            <div class="relative flex-1 flex flex-col max-w-xs w-full bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 shadow-2xl"
                 x-transition:enter="transition ease-in-out duration-300 transform"
                 x-transition:enter-start="-translate-x-full"
                 x-transition:enter-end="translate-x-0"
                 x-transition:leave="transition ease-in-out duration-300 transform"
                 x-transition:leave-start="translate-x-0"
                 x-transition:leave-end="-translate-x-full">

                <!-- Close Button -->
                <div class="absolute top-0 right-0 -mr-12 pt-2">
                    <button @click="sidebarOpen = false"
                            class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white transition-colors duration-200 hover:bg-white hover:bg-opacity-20">
                        <span class="sr-only">Close sidebar</span>
                        <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Mobile Sidebar Content -->
                <div class="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto">
                    <!-- Mobile Sidebar Header -->
                    <div class="relative flex items-center justify-between flex-shrink-0 px-6 py-6 border-b border-slate-700/50">
                        <!-- Background Pattern -->
                        <div class="absolute inset-0 bg-gradient-to-r from-primary-600/10 to-purple-600/10"></div>
                        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>

                        <div class="relative flex items-center space-x-4 w-full">
                            <div class="relative">
                                <div class="w-12 h-12 bg-gradient-to-br from-primary-500 via-primary-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg ring-2 ring-white/20 backdrop-blur-sm">
                                    <span x-html="currentSectionIcon" class="w-6 h-6 text-white drop-shadow-sm"></span>
                                </div>
                                <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-slate-900 animate-pulse"></div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h2 class="text-lg font-display font-bold text-white truncate" x-text="currentSectionTitle"></h2>
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                    <p class="text-xs text-slate-300 capitalize font-medium">{{ user.profile.get_role_display }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Navigation Menu -->
                    <nav class="flex-1 px-6 py-8 space-y-3">
                        <!-- Navigation Section Header -->
                        <div class="mb-6">
                            <h3 class="text-xs font-semibold text-slate-400 uppercase tracking-wider mb-3 px-3">Navigation</h3>
                            <div class="h-px bg-gradient-to-r from-slate-700 via-slate-600 to-slate-700"></div>
                        </div>

                        {% block mobile_sidebar_navigation %}
                        <!-- Mobile navigation - will be populated by JavaScript -->
                        <div id="mobile-nav-content" class="space-y-2">
                            <!-- Navigation items will be copied from desktop sidebar by JavaScript -->
                        </div>
                        {% endblock %}
                    </nav>

                    <!-- Mobile Sidebar Footer -->
                    <div class="flex-shrink-0 border-t border-slate-700/50 p-6 bg-slate-900/50 backdrop-blur-sm">
                        <!-- User Profile Section -->
                        <div class="relative">
                            <!-- Background Glow -->
                            <div class="absolute inset-0 bg-gradient-to-r from-primary-600/10 to-purple-600/10 rounded-xl blur-xl"></div>

                            <div class="relative bg-slate-800/50 rounded-xl p-4 border border-slate-700/30 backdrop-blur-sm">
                                <div class="flex items-center space-x-3">
                                    {% if user.profile.avatar %}
                                        <div class="relative">
                                            <img src="{{ user.profile.avatar.url }}" alt="Avatar" class="w-10 h-10 rounded-full object-cover ring-2 ring-primary-500/50">
                                            <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-slate-800"></div>
                                        </div>
                                    {% else %}
                                        <div class="relative">
                                            <div class="w-10 h-10 rounded-full bg-gradient-to-br from-primary-500 to-purple-600 flex items-center justify-center ring-2 ring-primary-500/50 shadow-lg">
                                                <span class="text-white font-bold text-sm">{{ user.first_name.0|default:user.username.0|upper }}</span>
                                            </div>
                                            <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-slate-800 animate-pulse"></div>
                                        </div>
                                    {% endif %}
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-semibold text-white truncate">{{ user.get_full_name|default:user.username }}</p>
                                        <p class="text-xs text-slate-400 truncate">{{ user.email }}</p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Mobile Quick Actions -->
                        <div class="mt-4 space-y-2">
                            <a href="{% url 'auth:profile' %}"
                               class="group flex items-center px-4 py-3 text-sm text-slate-300 hover:text-white hover:bg-slate-800/50 rounded-xl transition-all duration-200 border border-transparent hover:border-slate-700/50">
                                <div class="w-8 h-8 bg-slate-700/50 rounded-lg flex items-center justify-center mr-3 group-hover:bg-primary-600/20 transition-colors duration-200">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <span class="font-medium">Profile Settings</span>
                            </a>

                            <a href="{% url 'auth:logout' %}"
                               class="group flex items-center px-4 py-3 text-sm text-slate-300 hover:text-red-400 hover:bg-red-500/10 rounded-xl transition-all duration-200 border border-transparent hover:border-red-500/20">
                                <div class="w-8 h-8 bg-slate-700/50 rounded-lg flex items-center justify-center mr-3 group-hover:bg-red-500/20 transition-colors duration-200">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                    </svg>
                                </div>
                                <span class="font-medium">Sign Out</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex flex-col flex-1 lg:pl-72">
            <!-- Mobile Header -->
            <div class="lg:hidden bg-white border-b border-secondary-200 px-4 py-3 shadow-sm">
                <div class="flex items-center justify-between">
                    <!-- Mobile Menu Button -->
                    <button @click="sidebarOpen = true"
                            class="inline-flex items-center justify-center p-2 rounded-lg text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-colors duration-200">
                        <span class="sr-only">Open sidebar</span>
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>

                    <!-- Mobile Header Title -->
                    <h1 class="text-lg font-display font-semibold text-secondary-900" x-text="currentSectionTitle"></h1>

                    <!-- Mobile User Avatar -->
                    <div class="flex items-center">
                        {% if user.profile.avatar %}
                            <img src="{{ user.profile.avatar.url }}" alt="Avatar" class="w-8 h-8 rounded-full object-cover">
                        {% else %}
                            <div class="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center">
                                <span class="text-primary-600 font-medium text-sm">{{ user.first_name.0|default:user.username.0|upper }}</span>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto bg-secondary-50">
                <div class="py-6">
                    <!-- Page Header -->
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-2xl font-display font-bold text-secondary-900" x-text="currentSectionTitle"></h1>
                                <p class="mt-1 text-sm text-secondary-600">{% block page_description %}Welcome to your dashboard{% endblock %}</p>
                            </div>
                            <div class="flex items-center space-x-3">
                                {% block page_actions %}
                                <!-- Page-specific actions will be defined in child templates -->
                                {% endblock %}
                            </div>
                        </div>
                    </div>

                    <!-- Page Content -->
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-6">
                        {% block dashboard_content %}
                        <!-- Dashboard-specific content will be defined in child templates -->
                        {% endblock %}
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        <div class="fixed top-4 right-4 z-50 space-y-2" x-data="{ messages: [] }" x-init="
            {% for message in messages %}
                messages.push({
                    id: {{ forloop.counter }},
                    type: '{{ message.tags|default:'info' }}',
                    text: '{{ message|escapejs }}',
                    show: true
                });
            {% endfor %}
        ">
            <template x-for="message in messages.filter(m => m.show)" :key="message.id">
                <div x-show="message.show"
                     x-transition:enter="transition ease-out duration-300"
                     x-transition:enter-start="opacity-0 transform translate-x-full"
                     x-transition:enter-end="opacity-100 transform translate-x-0"
                     x-transition:leave="transition ease-in duration-200"
                     x-transition:leave-start="opacity-100 transform translate-x-0"
                     x-transition:leave-end="opacity-0 transform translate-x-full"
                     :class="{
                         'bg-green-50 border-green-400 text-green-800': message.type === 'success',
                         'bg-red-50 border-red-400 text-red-800': message.type === 'error',
                         'bg-yellow-50 border-yellow-400 text-yellow-800': message.type === 'warning',
                         'bg-blue-50 border-blue-400 text-blue-800': message.type === 'info'
                     }"
                     class="p-4 rounded-lg mb-4 border-l-4 max-w-sm shadow-lg">
                    <div class="flex justify-between items-start">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <svg x-show="message.type === 'success'" class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <svg x-show="message.type === 'error'" class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                </svg>
                                <svg x-show="message.type === 'warning'" class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                <svg x-show="message.type === 'info'" class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <p class="text-sm font-medium" x-text="message.text"></p>
                        </div>
                        <button @click="message.show = false" class="ml-4 flex-shrink-0 text-secondary-400 hover:text-secondary-600 transition-colors duration-200">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </template>
        </div>
    {% endif %}

    <!-- Enhanced JavaScript -->
    <script>
        // Configure HTMX with enhanced loading states
        document.body.addEventListener('htmx:configRequest', function(evt) {
            evt.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
        });

        // Enhanced loading states with spinners
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            const target = evt.target;
            if (target.classList.contains('btn')) {
                target.classList.add('loading');
                target.disabled = true;

                // Add spinner if it's a button
                const originalText = target.innerHTML;
                target.dataset.originalText = originalText;
                target.innerHTML = `
                    <div class="flex items-center justify-center">
                        <div class="spinner w-4 h-4 mr-2"></div>
                        <span>Loading...</span>
                    </div>
                `;
            }
        });

        document.body.addEventListener('htmx:afterRequest', function(evt) {
            const target = evt.target;
            if (target.classList.contains('btn')) {
                target.classList.remove('loading');
                target.disabled = false;

                // Restore original text
                if (target.dataset.originalText) {
                    target.innerHTML = target.dataset.originalText;
                    delete target.dataset.originalText;
                }
            }
        });

        // Auto-hide messages after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const messages = document.querySelectorAll('[x-data*="messages"]');
                messages.forEach(messageContainer => {
                    if (messageContainer._x_dataStack && messageContainer._x_dataStack[0].messages) {
                        messageContainer._x_dataStack[0].messages.forEach(message => {
                            setTimeout(() => {
                                message.show = false;
                            }, 5000);
                        });
                    }
                });
            }, 100);
        });
    </script>

    <!-- Dashboard Alpine.js Components -->
    <script>
        document.addEventListener('alpine:init', () => {
            // Main dashboard app component
            Alpine.data('dashboardApp', () => ({
                sidebarOpen: false,
                activeItem: 'overview',
                currentSectionTitle: 'Dashboard',
                currentSectionIcon: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>',

                // Section configurations
                sections: {
                    overview: {
                        title: 'Dashboard',
                        icon: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>'
                    },
                    users: {
                        title: 'User Management',
                        icon: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path></svg>'
                    },
                    listings: {
                        title: 'Listing Approval',
                        icon: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>'
                    },
                    reports: {
                        title: 'Reports & Analytics',
                        icon: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>'
                    }
                },

                init() {
                    // Set initial state based on current page
                    const currentPath = window.location.pathname;
                    if (currentPath.includes('/admin/users/')) {
                        this.setActive('users');
                    } else if (currentPath.includes('/admin/listings/')) {
                        this.setActive('listings');
                    } else if (currentPath.includes('/admin/analytics/')) {
                        this.setActive('reports');
                    } else {
                        this.setActive('overview');
                    }
                },

                isActive(item) {
                    return this.activeItem === item;
                },

                setActive(item) {
                    this.activeItem = item;
                    if (this.sections[item]) {
                        this.currentSectionTitle = this.sections[item].title;
                        this.currentSectionIcon = this.sections[item].icon;
                    }
                    // Close mobile sidebar when navigation item is clicked
                    this.sidebarOpen = false;
                }
            }));

            // Sidebar navigation component for backward compatibility
            Alpine.data('sidebarNav', () => ({
                activeItem: 'overview',

                init() {
                    // Sync with main dashboard app
                    const dashboardApp = document.querySelector('[x-data*="dashboardApp"]');
                    if (dashboardApp && dashboardApp._x_dataStack) {
                        this.activeItem = dashboardApp._x_dataStack[0].activeItem;
                    }
                },

                isActive(item) {
                    return this.activeItem === item;
                },

                setActive(item) {
                    this.activeItem = item;
                    // Update the main dashboard app state
                    const dashboardApp = document.querySelector('[x-data*="dashboardApp"]');
                    if (dashboardApp && dashboardApp._x_dataStack) {
                        dashboardApp._x_dataStack[0].setActive(item);
                    }
                }
            }));
        });

        // Enhanced mobile navigation setup
        document.addEventListener('DOMContentLoaded', function() {
            // Function to copy navigation from desktop to mobile
            const copyNavigationToMobile = () => {
                const desktopNavigation = document.querySelector('.lg\\:w-72 [x-data*="sidebarNav"]');
                const mobileNavContent = document.getElementById('mobile-nav-content');

                if (desktopNavigation && mobileNavContent) {
                    // Clone the desktop navigation
                    const clonedNav = desktopNavigation.cloneNode(true);

                    // Replace the mobile navigation content
                    mobileNavContent.innerHTML = clonedNav.innerHTML;

                    // Initialize Alpine.js on the cloned content
                    if (window.Alpine) {
                        Alpine.initTree(mobileNavContent);
                    }
                }
            };

            // Function to add mobile-specific click handlers
            const addMobileClickHandlers = () => {
                const mobileNavLinks = document.querySelectorAll('#mobile-nav-content a[href]');
                mobileNavLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        // Close mobile sidebar when any navigation link is clicked
                        const dashboardApp = document.querySelector('[x-data*="dashboardApp"]');
                        if (dashboardApp && dashboardApp._x_dataStack) {
                            dashboardApp._x_dataStack[0].sidebarOpen = false;
                        }
                    });
                });
            };

            // Initial setup - wait for Alpine.js to initialize
            setTimeout(() => {
                copyNavigationToMobile();
                addMobileClickHandlers();
            }, 100);

            // Re-run when Alpine.js is fully loaded
            document.addEventListener('alpine:initialized', () => {
                setTimeout(() => {
                    copyNavigationToMobile();
                    addMobileClickHandlers();
                }, 50);
            });
        });

        // Keyboard navigation improvements
        document.addEventListener('keydown', function(e) {
            // Escape key closes dropdowns and mobile sidebar
            if (e.key === 'Escape') {
                // Close Alpine.js dropdowns and mobile sidebar
                document.querySelectorAll('[x-data]').forEach(el => {
                    if (el._x_dataStack && el._x_dataStack[0].sidebarOpen !== undefined) {
                        el._x_dataStack[0].sidebarOpen = false;
                    }
                });
            }
        });

        // Page transition effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add entrance animation to main content
            const main = document.querySelector('main');
            if (main) {
                main.style.opacity = '0';
                main.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    main.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
                    main.style.opacity = '1';
                    main.style.transform = 'translateY(0)';
                }, 100);
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>