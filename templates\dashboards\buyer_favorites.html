{% extends 'dashboards/base_dashboard.html' %}

{% block dashboard_title %}My Favorites{% endblock %}
{% block active_nav_item %}favorites{% endblock %}

{% block sidebar_icon %}
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
</svg>
{% endblock %}

{% block sidebar_title %}Buyer Hub{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_dashboard' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Overview' item_key='overview' %}

    <!-- Browse Listings -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_browse_listings' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>' label='Browse Listings' item_key='browse' %}

    <!-- Favorites -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_favorites' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path></svg>' label='Favorites' item_key='favorites' %}

    <!-- My Inquiries -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_inquiries' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>' label='My Inquiries' item_key='inquiries' %}
</div>
{% endblock %}

{% block page_title %}My Favorite Properties{% endblock %}
{% block page_description %}Properties you've saved for later review{% endblock %}

{% block page_actions %}
<a href="{% url 'buyer_browse_listings' %}" class="btn btn-primary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
    </svg>
    Browse More Properties
</a>
{% endblock %}

{% block dashboard_content %}
<div class="flex flex-col lg:flex-row gap-6">
    <!-- Filters Sidebar -->
    <div class="lg:w-80 flex-shrink-0">
        <div class="card sticky top-6">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Filter Favorites</h3>
                <p class="text-sm text-secondary-600">{{ page_obj.paginator.count }} favorite{{ page_obj.paginator.count|pluralize }} saved</p>
            </div>
            <div class="card-body">
                <form method="get" class="space-y-4">
                    <!-- Property Type -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">Property Type</label>
                        <select name="property_type" class="form-select" onchange="this.form.submit()">
                            <option value="">All Types</option>
                            {% for value, label in property_types %}
                                <option value="{{ value }}" {% if property_type_filter == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Sort By -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">Sort By</label>
                        <select name="sort" class="form-select" onchange="this.form.submit()">
                            <option value="newest" {% if sort_by == 'newest' %}selected{% endif %}>Recently Added</option>
                            <option value="oldest" {% if sort_by == 'oldest' %}selected{% endif %}>Oldest First</option>
                            <option value="price_low" {% if sort_by == 'price_low' %}selected{% endif %}>Price: Low to High</option>
                            <option value="price_high" {% if sort_by == 'price_high' %}selected{% endif %}>Price: High to Low</option>
                            <option value="size_low" {% if sort_by == 'size_low' %}selected{% endif %}>Size: Small to Large</option>
                            <option value="size_high" {% if sort_by == 'size_high' %}selected{% endif %}>Size: Large to Small</option>
                        </select>
                    </div>

                    <!-- Clear Filters -->
                    <div class="pt-4 border-t border-secondary-200">
                        <a href="{% url 'buyer_favorites' %}" class="btn btn-secondary btn-sm w-full">
                            Clear Filters
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Favorites Grid -->
    <div class="flex-1">
        {% if page_obj %}
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {% for favorite in page_obj %}
                    <div class="card hover:shadow-lg transition-all duration-200 group">
                        <!-- Image Container -->
                        <div class="relative overflow-hidden rounded-t-lg">
                            {% if favorite.land.images.all %}
                                {% with favorite.land.images.all|first as primary_image %}
                                    <img src="{{ primary_image.image.url }}" 
                                         alt="{{ primary_image.alt_text|default:favorite.land.title }}"
                                         class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200">
                                {% endwith %}
                            {% else %}
                                <div class="w-full h-48 bg-secondary-200 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            {% endif %}
                            
                            <!-- Property Type Badge -->
                            <div class="absolute top-3 left-3">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white bg-opacity-90 text-secondary-800">
                                    {{ favorite.land.get_property_type_display }}
                                </span>
                            </div>
                            
                            <!-- Remove from Favorites Button -->
                            <div class="absolute top-3 right-3">
                                <button hx-post="{% url 'buyer_toggle_favorite' favorite.land.id %}"
                                        hx-target="closest .card"
                                        hx-swap="outerHTML"
                                        class="w-8 h-8 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full flex items-center justify-center transition-all duration-200 group-hover:scale-110">
                                    <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>
                            
                            <!-- Favorited Date -->
                            <div class="absolute bottom-3 left-3">
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-black bg-opacity-50 text-white">
                                    Saved {{ favorite.created_at|timesince }} ago
                                </span>
                            </div>
                        </div>
                        
                        <!-- Card Content -->
                        <div class="card-body">
                            <!-- Title and Location -->
                            <div class="mb-3">
                                <h3 class="text-lg font-semibold text-secondary-900 mb-1 line-clamp-2">
                                    <a href="{% url 'buyer_listing_detail' favorite.land.id %}" 
                                       class="hover:text-primary-600 transition-colors duration-200">
                                        {{ favorite.land.title }}
                                    </a>
                                </h3>
                                <p class="text-sm text-secondary-600 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    {{ favorite.land.location }}
                                </p>
                            </div>
                            
                            <!-- Property Details -->
                            <div class="flex items-center justify-between text-sm text-secondary-600 mb-4">
                                <div class="flex items-center space-x-4">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                                        </svg>
                                        {{ favorite.land.size_acres }} acres
                                    </span>
                                </div>
                                <span class="text-xs text-secondary-500">
                                    Listed {{ favorite.land.created_at|timesince }} ago
                                </span>
                            </div>
                            
                            <!-- Description -->
                            <p class="text-sm text-secondary-600 mb-4 line-clamp-2">
                                {{ favorite.land.description|truncatewords:20 }}
                            </p>
                            
                            <!-- Price and Actions -->
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-2xl font-bold text-secondary-900">
                                        ${{ favorite.land.price|floatformat:0 }}
                                    </p>
                                    <p class="text-xs text-secondary-500">
                                        ${{ favorite.land.price|div:favorite.land.size_acres|floatformat:0 }}/acre
                                    </p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <a href="{% url 'buyer_listing_detail' favorite.land.id %}" 
                                       class="btn btn-primary btn-sm">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <div class="mt-8 flex items-center justify-between">
                    <div class="flex-1 flex justify-between sm:hidden">
                        {% if page_obj.has_previous %}
                            <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
                               class="btn btn-secondary btn-md">Previous</a>
                        {% endif %}
                        {% if page_obj.has_next %}
                            <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" 
                               class="btn btn-secondary btn-md">Next</a>
                        {% endif %}
                    </div>
                    
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-secondary-700">
                                Showing
                                <span class="font-medium">{{ page_obj.start_index }}</span>
                                to
                                <span class="font-medium">{{ page_obj.end_index }}</span>
                                of
                                <span class="font-medium">{{ page_obj.paginator.count }}</span>
                                favorites
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% if page_obj.has_previous %}
                                    <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50">
                                        <span class="sr-only">Previous</span>
                                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-primary-500 bg-primary-50 text-sm font-medium text-primary-600">
                                            {{ num }}
                                        </span>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}" 
                                           class="relative inline-flex items-center px-4 py-2 border border-secondary-300 bg-white text-sm font-medium text-secondary-700 hover:bg-secondary-50">
                                            {{ num }}
                                        </a>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50">
                                        <span class="sr-only">Next</span>
                                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <!-- No Favorites -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-secondary-900">No favorites yet</h3>
                <p class="mt-1 text-sm text-secondary-500">Start browsing properties and save your favorites to see them here.</p>
                <div class="mt-6">
                    <a href="{% url 'buyer_browse_listings' %}" class="btn btn-primary btn-md">
                        Browse Properties
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}