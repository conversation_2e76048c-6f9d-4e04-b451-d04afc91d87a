{% extends 'dashboards/base_dashboard.html' %}
{% load form_tags %}

{% block dashboard_title %}Market Insights{% endblock %}
{% block active_nav_item %}insights{% endblock %}

{% block sidebar_icon %}
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
</svg>
{% endblock %}

{% block sidebar_title %}Buyer Hub{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()" class="space-y-2">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_dashboard' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Dashboard' description='Overview & insights' item_key='overview' %}

    <!-- Browse Listings -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_browse_listings' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>' label='Browse Listings' description='Discover properties' item_key='browse' %}

    <!-- Favorites -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_favorites' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path></svg>' label='Favorites' description='Saved properties' item_key='favorites' %}

    <!-- My Inquiries -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_inquiries' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>' label='My Inquiries' description='Track communications' item_key='inquiries' %}

    <!-- Saved Searches -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_saved_searches' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path></svg>' label='Saved Searches' description='Search alerts' item_key='searches' %}

    <!-- Market Insights -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_market_insights' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Market Insights' description='Trends & analytics' item_key='insights' %}

    <!-- Divider -->
    <div class="my-6">
        <div class="h-px bg-gradient-to-r from-transparent via-slate-600 to-transparent"></div>
    </div>

    <!-- Buying Guide -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_buying_guide' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>' label='Buying Guide' description='Tips & resources' item_key='guide' %}

    <!-- Help & Support -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_support' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>' label='Support' description='Help & documentation' item_key='support' %}
</div>
{% endblock %}

{% block page_title %}Market Insights{% endblock %}
{% block page_description %}Land market trends, analytics, and insights{% endblock %}

{% block page_actions %}
<a href="{% url 'buyer_browse_listings' %}" class="btn btn-primary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
    </svg>
    Browse Properties
</a>
{% endblock %}

{% block dashboard_content %}
<!-- Market Overview Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Listings -->
    <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-secondary-600">Total Listings</p>
                <p class="text-2xl font-bold text-secondary-900">{{ total_listings }}</p>
            </div>
        </div>
    </div>

    <!-- Recent Listings -->
    <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-secondary-600">New This Month</p>
                <p class="text-2xl font-bold text-secondary-900">{{ recent_listings_count }}</p>
            </div>
        </div>
    </div>

    <!-- Property Types -->
    <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-secondary-600">Property Types</p>
                <p class="text-2xl font-bold text-secondary-900">{{ property_type_stats|length }}</p>
            </div>
        </div>
    </div>

    <!-- Average Price -->
    <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-secondary-600">Avg. Price</p>
                <p class="text-2xl font-bold text-secondary-900">
                    {% if avg_prices %}
                        ${{ avg_prices.0.avg_price|floatformat:0 }}
                    {% else %}
                        N/A
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Property Type Distribution -->
    <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
        <div class="px-6 py-5 border-b border-secondary-100">
            <h3 class="text-xl font-bold text-secondary-900">Property Type Distribution</h3>
            <p class="text-sm text-secondary-600 mt-1">Breakdown by property type</p>
        </div>
        <div class="p-6">
            {% if property_type_stats %}
                <div class="space-y-4">
                    {% for stat in property_type_stats %}
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-blue-500 rounded mr-3"></div>
                                <span class="text-sm font-medium text-secondary-900">{{ stat.property_type|capfirst }}</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-secondary-600">{{ stat.count }}</span>
                                <div class="w-20 bg-secondary-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: {% widthratio stat.count total_listings 100 %}%"></div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <p class="text-center text-secondary-500">No data available</p>
            {% endif %}
        </div>
    </div>

    <!-- Price Ranges -->
    <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
        <div class="px-6 py-5 border-b border-secondary-100">
            <h3 class="text-xl font-bold text-secondary-900">Price Distribution</h3>
            <p class="text-sm text-secondary-600 mt-1">Properties by price range</p>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                {% for range in price_ranges %}
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-green-500 rounded mr-3"></div>
                            <span class="text-sm font-medium text-secondary-900">{{ range.range }}</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-secondary-600">{{ range.count }}</span>
                            <div class="w-20 bg-secondary-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: {% widthratio range.count total_listings 100 %}%"></div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Average Prices by Type -->
<div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
    <div class="px-6 py-5 border-b border-secondary-100">
        <h3 class="text-xl font-bold text-secondary-900">Average Prices by Property Type</h3>
        <p class="text-sm text-secondary-600 mt-1">Market pricing insights</p>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-secondary-200">
            <thead class="bg-secondary-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Property Type</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Average Price</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Average Size</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Price per Acre</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-secondary-200">
                {% for price_data in avg_prices %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-secondary-900">
                            {{ price_data.property_type|capfirst }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                            ${{ price_data.avg_price|floatformat:0 }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                            {{ price_data.avg_size|floatformat:1 }} acres
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                            ${{ price_data.avg_price|div:price_data.avg_size|floatformat:0 }}/acre
                        </td>
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-sm text-secondary-500">
                            No pricing data available
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
