{% extends 'dashboards/base_dashboard.html' %}
{% load form_tags %}

{% block dashboard_title %}Comprehensive Reports{% endblock %}
{% block active_nav_item %}reports{% endblock %}

{% block sidebar_icon %}
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
</svg>
{% endblock %}

{% block sidebar_title %}Seller Hub{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()" class="space-y-2">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='seller_dashboard' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Dashboard' description='Overview & insights' item_key='overview' %}

    <!-- My Listings -->
    {% include 'components/sidebar_nav_item.html' with href='seller_listings' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>' label='My Listings' description='Manage properties' item_key='listings' %}

    <!-- Create Listing -->
    {% include 'components/sidebar_nav_item.html' with href='seller_create_listing' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>' label='Create Listing' description='Add new property' item_key='create' %}

    <!-- Inquiries -->
    {% include 'components/sidebar_nav_item.html' with href='seller_inquiries' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>' label='Inquiries' description='Buyer communications' item_key='inquiries' %}

    <!-- Comprehensive Reports -->
    {% include 'components/sidebar_nav_item.html' with href='seller_reports' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Comprehensive Reports' description='Analytics, insights & resources' item_key='reports' %}
</div>
{% endblock %}

{% block page_title %}Comprehensive Reports{% endblock %}
{% block page_description %}Performance analytics, market insights, resources, and support in one unified dashboard{% endblock %}

{% block page_actions %}
<a href="{% url 'seller_create_listing' %}" class="btn btn-primary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>
    Create New Listing
</a>
{% endblock %}

{% block dashboard_content %}
<!-- Navigation Tabs -->
<div class="mb-8" x-data="{ activeTab: 'performance' }">
    <div class="border-b border-secondary-200">
        <nav class="-mb-px flex space-x-8">
            <button @click="activeTab = 'performance'" 
                    :class="activeTab === 'performance' ? 'border-blue-500 text-blue-600' : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'"
                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Performance Analytics
            </button>
            <button @click="activeTab = 'market'" 
                    :class="activeTab === 'market' ? 'border-blue-500 text-blue-600' : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'"
                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Market Insights
            </button>
            <button @click="activeTab = 'resources'" 
                    :class="activeTab === 'resources' ? 'border-blue-500 text-blue-600' : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'"
                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Resources & Tips
            </button>
            <button @click="activeTab = 'support'" 
                    :class="activeTab === 'support' ? 'border-blue-500 text-blue-600' : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'"
                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Support & Help
            </button>
        </nav>
    </div>

    <!-- Performance Analytics Tab -->
    <div x-show="activeTab === 'performance'" class="mt-8">
        <!-- Performance Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Portfolio Value -->
            <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Portfolio Value</p>
                        <p class="text-2xl font-bold text-secondary-900">${{ total_value|floatformat:0 }}</p>
                    </div>
                </div>
            </div>

            <!-- Average Price -->
            <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Average Price</p>
                        <p class="text-2xl font-bold text-secondary-900">${{ avg_price|floatformat:0 }}</p>
                    </div>
                </div>
            </div>

            <!-- Response Rate -->
            <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Response Rate</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ response_rate|floatformat:1 }}%</p>
                    </div>
                </div>
            </div>

            <!-- Inquiries per Listing -->
            <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Avg. Inquiries</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ inquiries_per_listing|floatformat:1 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Analytics -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Monthly Inquiry Trends -->
            <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
                <div class="px-6 py-5 border-b border-secondary-100">
                    <h3 class="text-xl font-bold text-secondary-900">Inquiry Trends</h3>
                    <p class="text-sm text-secondary-600 mt-1">Monthly inquiry activity</p>
                </div>
                <div class="p-6">
                    {% if monthly_inquiries %}
                        <div class="space-y-4">
                            {% for month_data in monthly_inquiries %}
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-secondary-900">{{ month_data.month }}</span>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-secondary-600">{{ month_data.count }}</span>
                                        <div class="w-20 bg-secondary-200 rounded-full h-2">
                                            {% if monthly_inquiries %}
                                                {% with max_count=monthly_inquiries|first %}
                                                    <div class="bg-blue-500 h-2 rounded-full" style="width: {% if max_count.count > 0 %}{% widthratio month_data.count max_count.count 100 %}{% else %}0{% endif %}%"></div>
                                                {% endwith %}
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-center text-secondary-500">No inquiry data available</p>
                    {% endif %}
                </div>
            </div>

            <!-- Property Type Performance -->
            <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
                <div class="px-6 py-5 border-b border-secondary-100">
                    <h3 class="text-xl font-bold text-secondary-900">Performance by Type</h3>
                    <p class="text-sm text-secondary-600 mt-1">Inquiries by property type</p>
                </div>
                <div class="p-6">
                    {% if property_type_performance %}
                        <div class="space-y-4">
                            {% for type_data in property_type_performance %}
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-4 h-4 bg-green-500 rounded mr-3"></div>
                                        <span class="text-sm font-medium text-secondary-900">{{ type_data.property_type|capfirst }}</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-secondary-600">{{ type_data.inquiry_count }} inquiries</span>
                                        <div class="w-20 bg-secondary-200 rounded-full h-2">
                                            {% if property_type_performance %}
                                                {% with max_inquiries=property_type_performance|first %}
                                                    <div class="bg-green-500 h-2 rounded-full" style="width: {% if max_inquiries.inquiry_count > 0 %}{% widthratio type_data.inquiry_count max_inquiries.inquiry_count 100 %}{% else %}0{% endif %}%"></div>
                                                {% endwith %}
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-center text-secondary-500">No performance data available</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Top Performing Listings -->
        <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
            <div class="px-6 py-5 border-b border-secondary-100">
                <h3 class="text-xl font-bold text-secondary-900">Top Performing Listings</h3>
                <p class="text-sm text-secondary-600 mt-1">Your most popular properties</p>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-secondary-200">
                    <thead class="bg-secondary-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Property</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Price</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Inquiries</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Favorites</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-secondary-200">
                        {% for listing in top_listings %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            {% if listing.images.first %}
                                                <img class="h-10 w-10 rounded-lg object-cover" src="{{ listing.images.first.image.url }}" alt="{{ listing.title }}">
                                            {% else %}
                                                <div class="h-10 w-10 rounded-lg bg-secondary-200 flex items-center justify-center">
                                                    <svg class="w-5 h-5 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                    </svg>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-secondary-900">{{ listing.title }}</div>
                                            <div class="text-sm text-secondary-500">{{ listing.location }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                                    ${{ listing.price|floatformat:0 }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                                    {{ listing.inquiry_count }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                                    {{ listing.favorited_by.count }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if listing.status == 'approved' %}bg-green-100 text-green-800
                                        {% elif listing.status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ listing.get_status_display }}
                                    </span>
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-sm text-secondary-500">
                                    No listings with inquiries yet
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Market Insights Tab -->
    <div x-show="activeTab === 'market'" class="mt-8">
        <!-- Market Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Market Share -->
            <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Market Share</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ seller_market_share|floatformat:1 }}%</p>
                    </div>
                </div>
            </div>

            <!-- Price Comparison -->
            <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-{% if price_comparison >= 0 %}green{% else %}red{% endif %}-500 to-{% if price_comparison >= 0 %}green{% else %}red{% endif %}-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            {% if price_comparison >= 0 %}
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            {% else %}
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                            {% endif %}
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">vs Market Avg</p>
                        <p class="text-2xl font-bold text-secondary-900">
                            {% if price_comparison >= 0 %}+{% endif %}{{ price_comparison|floatformat:1 }}%
                        </p>
                    </div>
                </div>
            </div>

            <!-- Market Listings -->
            <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Total Market</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ total_market_listings }}</p>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">New This Month</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ recent_market_listings }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommendations -->
        {% if recommendations %}
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 mb-8 border border-blue-100">
            <h3 class="text-lg font-bold text-secondary-900 mb-4">Market Recommendations</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                {% for rec in recommendations %}
                    <div class="bg-white rounded-xl p-4 border border-blue-200">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-{% if rec.priority == 'high' %}red{% elif rec.priority == 'medium' %}yellow{% else %}green{% endif %}-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-4 h-4 text-{% if rec.priority == 'high' %}red{% elif rec.priority == 'medium' %}yellow{% else %}green{% endif %}-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-secondary-900 mb-1">{{ rec.title }}</h4>
                                <p class="text-sm text-secondary-600">{{ rec.description }}</p>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Competitive Analysis -->
        {% if competitive_analysis %}
        <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
            <div class="px-6 py-5 border-b border-secondary-100">
                <h3 class="text-xl font-bold text-secondary-900">Competitive Analysis</h3>
                <p class="text-sm text-secondary-600 mt-1">How your listings compare to similar properties</p>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-secondary-200">
                    <thead class="bg-secondary-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Your Property</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Your Price</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Competitors</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Market Avg</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">Position</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-secondary-200">
                        {% for analysis in competitive_analysis %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-secondary-900">{{ analysis.listing.title }}</div>
                                    <div class="text-sm text-secondary-500">{{ analysis.listing.location }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                                    ${{ analysis.listing.price|floatformat:0 }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                                    {{ analysis.competitor_count }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                                    ${{ analysis.avg_competitor_price|floatformat:0 }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if analysis.price_position == 'below_market' %}bg-green-100 text-green-800
                                        {% elif analysis.price_position == 'above_market' %}bg-red-100 text-red-800
                                        {% else %}bg-blue-100 text-blue-800{% endif %}">
                                        {% if analysis.price_position == 'below_market' %}Below Market
                                        {% elif analysis.price_position == 'above_market' %}Above Market
                                        {% else %}Competitive{% endif %}
                                    </span>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Resources & Tips Tab -->
    <div x-show="activeTab === 'resources'" class="mt-8">
        <!-- Success Tips -->
        <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 mb-8 border border-green-100">
            <h3 class="text-lg font-bold text-secondary-900 mb-4">💡 Success Tips</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="bg-white rounded-xl p-4 border border-green-200">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-secondary-900 mb-1">High-quality photos increase inquiries by up to 40%</p>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Photography
                            </span>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl p-4 border border-green-200">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-secondary-900 mb-1">Properties with detailed descriptions sell 25% faster</p>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Content
                            </span>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl p-4 border border-green-200">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-secondary-900 mb-1">Competitive pricing attracts 60% more potential buyers</p>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Pricing
                            </span>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl p-4 border border-green-200">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-secondary-900 mb-1">Responding to inquiries within 2 hours improves conversion by 35%</p>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Communication
                            </span>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl p-4 border border-green-200">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-secondary-900 mb-1">Virtual tours increase buyer engagement by 50%</p>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Technology
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Resource Categories -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Listing Optimization -->
            <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
                <div class="px-6 py-5 border-b border-secondary-100 bg-gradient-to-r from-secondary-50/50 to-transparent">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-secondary-900">Listing Optimization</h3>
                            <p class="text-sm text-secondary-600">Maximize visibility and appeal</p>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="border border-secondary-200 rounded-lg p-4">
                            <h4 class="font-semibold text-secondary-900 mb-2">Writing Compelling Property Descriptions</h4>
                            <p class="text-sm text-secondary-600 mb-3">Learn how to craft descriptions that attract buyers and highlight key features.</p>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Guide</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Beginner</span>
                            </div>
                        </div>
                        <div class="border border-secondary-200 rounded-lg p-4">
                            <h4 class="font-semibold text-secondary-900 mb-2">Professional Photography Tips</h4>
                            <p class="text-sm text-secondary-600 mb-3">Best practices for taking photos that showcase your property effectively.</p>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Guide</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Intermediate</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Marketing & Promotion -->
            <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
                <div class="px-6 py-5 border-b border-secondary-100 bg-gradient-to-r from-secondary-50/50 to-transparent">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-secondary-900">Marketing & Promotion</h3>
                            <p class="text-sm text-secondary-600">Effective promotion strategies</p>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="border border-secondary-200 rounded-lg p-4">
                            <h4 class="font-semibold text-secondary-900 mb-2">Social Media Marketing for Land Sales</h4>
                            <p class="text-sm text-secondary-600 mb-3">Leverage social platforms to reach potential buyers.</p>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Guide</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Beginner</span>
                            </div>
                        </div>
                        <div class="border border-secondary-200 rounded-lg p-4">
                            <h4 class="font-semibold text-secondary-900 mb-2">Creating Virtual Tours</h4>
                            <p class="text-sm text-secondary-600 mb-3">Tools and techniques for creating engaging virtual property tours.</p>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Tutorial</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">Advanced</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Support & Help Tab -->
    <div x-show="activeTab === 'support'" class="mt-8">
        <!-- Quick Help Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 p-6 text-center hover:shadow-lg transition-shadow duration-200">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">Email Support</h3>
                <p class="text-sm text-secondary-600 mb-3">Get detailed help via email</p>
                <div class="text-sm font-medium text-secondary-900 mb-2"><EMAIL></div>
                <div class="text-xs text-secondary-500">Within 24 hours</div>
            </div>

            <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 p-6 text-center hover:shadow-lg transition-shadow duration-200">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">Phone Support</h3>
                <p class="text-sm text-secondary-600 mb-3">Speak directly with our team</p>
                <div class="text-sm font-medium text-secondary-900 mb-2">1-800-LANDHUB</div>
                <div class="text-xs text-secondary-500">Mon-Fri 9AM-6PM EST</div>
            </div>

            <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 p-6 text-center hover:shadow-lg transition-shadow duration-200">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">Live Chat</h3>
                <p class="text-sm text-secondary-600 mb-3">Instant help during business hours</p>
                <div class="text-sm font-medium text-secondary-900 mb-2">Available in dashboard</div>
                <div class="text-xs text-secondary-500">Immediate</div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
            <div class="px-8 py-6 border-b border-secondary-100 bg-gradient-to-r from-secondary-50/50 to-transparent">
                <h3 class="text-xl font-bold text-secondary-900">Frequently Asked Questions</h3>
            </div>
            <div class="p-8">
                <div class="space-y-6" x-data="{ openFaq: null }">
                    <div class="border border-secondary-200 rounded-xl overflow-hidden">
                        <button @click="openFaq = openFaq === 'faq1' ? null : 'faq1'"
                                class="w-full px-6 py-4 text-left bg-secondary-50 hover:bg-secondary-100 transition-colors duration-200 flex items-center justify-between">
                            <span class="font-medium text-secondary-900">How do I create my first listing?</span>
                            <svg class="w-5 h-5 text-secondary-500 transform transition-transform duration-200"
                                 :class="{ 'rotate-180': openFaq === 'faq1' }"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div x-show="openFaq === 'faq1'"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform -translate-y-2"
                             x-transition:enter-end="opacity-100 transform translate-y-0"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform translate-y-0"
                             x-transition:leave-end="opacity-0 transform -translate-y-2"
                             class="px-6 py-4 bg-white border-t border-secondary-200">
                            <p class="text-secondary-700">Click "Create New Listing" from your dashboard. Fill in all required fields including title, description, price, and location. Add high-quality photos to make your listing stand out.</p>
                        </div>
                    </div>

                    <div class="border border-secondary-200 rounded-xl overflow-hidden">
                        <button @click="openFaq = openFaq === 'faq2' ? null : 'faq2'"
                                class="w-full px-6 py-4 text-left bg-secondary-50 hover:bg-secondary-100 transition-colors duration-200 flex items-center justify-between">
                            <span class="font-medium text-secondary-900">How do I respond to buyer inquiries?</span>
                            <svg class="w-5 h-5 text-secondary-500 transform transition-transform duration-200"
                                 :class="{ 'rotate-180': openFaq === 'faq2' }"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div x-show="openFaq === 'faq2'"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform -translate-y-2"
                             x-transition:enter-end="opacity-100 transform translate-y-0"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform translate-y-0"
                             x-transition:leave-end="opacity-0 transform -translate-y-2"
                             class="px-6 py-4 bg-white border-t border-secondary-200">
                            <p class="text-secondary-700">Go to your Inquiries section to see all messages from potential buyers. Click on any inquiry to read the full message and respond directly through the platform.</p>
                        </div>
                    </div>

                    <div class="border border-secondary-200 rounded-xl overflow-hidden">
                        <button @click="openFaq = openFaq === 'faq3' ? null : 'faq3'"
                                class="w-full px-6 py-4 text-left bg-secondary-50 hover:bg-secondary-100 transition-colors duration-200 flex items-center justify-between">
                            <span class="font-medium text-secondary-900">How much does it cost to list my property?</span>
                            <svg class="w-5 h-5 text-secondary-500 transform transition-transform duration-200"
                                 :class="{ 'rotate-180': openFaq === 'faq3' }"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div x-show="openFaq === 'faq3'"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform -translate-y-2"
                             x-transition:enter-end="opacity-100 transform translate-y-0"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform translate-y-0"
                             x-transition:leave-end="opacity-0 transform -translate-y-2"
                             class="px-6 py-4 bg-white border-t border-secondary-200">
                            <p class="text-secondary-700">Basic listings are free. We offer premium features like featured placement and enhanced marketing tools for a small fee. Check our pricing page for current rates.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
