from django.urls import path
from .views import (
    home, admin_dashboard, seller_dashboard, buyer_dashboard,
    admin_user_management, admin_toggle_user_status,
    admin_listing_approval, admin_approve_listing, admin_reject_listing,
    admin_analytics, seller_listings, seller_create_listing, seller_edit_listing,
    seller_delete_image, seller_set_primary_image, seller_inquiries,
    seller_inquiry_detail, seller_mark_inquiry_read, seller_reports,
    buyer_browse_listings, buyer_listing_detail, buyer_favorites,
    buyer_toggle_favorite, buyer_inquiries, buyer_send_inquiry,
    buyer_saved_searches, buyer_market_insights, buyer_buying_guide, buyer_support
)

urlpatterns = [
    path('', home, name='home'),
    path('dashboard/', home, name='dashboard'),  # Temporary redirect
    path('admin-dashboard/', admin_dashboard, name='admin_dashboard'),
    path('seller-dashboard/', seller_dashboard, name='seller_dashboard'),
    path('buyer-dashboard/', buyer_dashboard, name='buyer_dashboard'),
    
    # Admin-specific URLs
    path('dashboard/users/', admin_user_management, name='admin_user_management'),
    path('dashboard/users/<int:user_id>/toggle-status/', admin_toggle_user_status, name='admin_toggle_user_status'),
    path('dashboard/listings/', admin_listing_approval, name='admin_listing_approval'),
    path('dashboard/listings/<int:listing_id>/approve/', admin_approve_listing, name='admin_approve_listing'),
    path('dashboard/listings/<int:listing_id>/reject/', admin_reject_listing, name='admin_reject_listing'),
    path('dashboard/analytics/', admin_analytics, name='admin_analytics'),
    
    # Seller-specific URLs
    path('seller/listings/', seller_listings, name='seller_listings'),
    path('seller/listings/create/', seller_create_listing, name='seller_create_listing'),
    path('seller/listings/<int:listing_id>/edit/', seller_edit_listing, name='seller_edit_listing'),
    path('seller/images/<int:image_id>/delete/', seller_delete_image, name='seller_delete_image'),
    path('seller/images/<int:image_id>/set-primary/', seller_set_primary_image, name='seller_set_primary_image'),
    path('seller/inquiries/', seller_inquiries, name='seller_inquiries'),
    path('seller/inquiries/<int:inquiry_id>/', seller_inquiry_detail, name='seller_inquiry_detail'),
    path('seller/inquiries/<int:inquiry_id>/mark-read/', seller_mark_inquiry_read, name='seller_mark_inquiry_read'),
    path('seller/reports/', seller_reports, name='seller_reports'),
    
    # Buyer-specific URLs
    path('buyer/browse/', buyer_browse_listings, name='buyer_browse_listings'),
    path('buyer/listings/<int:listing_id>/', buyer_listing_detail, name='buyer_listing_detail'),
    path('buyer/favorites/', buyer_favorites, name='buyer_favorites'),
    path('buyer/favorites/<int:listing_id>/toggle/', buyer_toggle_favorite, name='buyer_toggle_favorite'),
    path('buyer/inquiries/', buyer_inquiries, name='buyer_inquiries'),
    path('buyer/inquiries/<int:listing_id>/send/', buyer_send_inquiry, name='buyer_send_inquiry'),
    path('buyer/saved-searches/', buyer_saved_searches, name='buyer_saved_searches'),
    path('buyer/market-insights/', buyer_market_insights, name='buyer_market_insights'),
    path('buyer/buying-guide/', buyer_buying_guide, name='buyer_buying_guide'),
    path('buyer/support/', buyer_support, name='buyer_support'),
]
