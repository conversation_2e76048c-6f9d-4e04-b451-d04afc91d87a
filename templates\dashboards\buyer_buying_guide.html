{% extends 'dashboards/base_dashboard.html' %}
{% load form_tags %}

{% block dashboard_title %}Buying Guide{% endblock %}
{% block active_nav_item %}guide{% endblock %}

{% block sidebar_icon %}
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
</svg>
{% endblock %}

{% block sidebar_title %}Buyer Hub{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()" class="space-y-2">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_dashboard' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Dashboard' description='Overview & insights' item_key='overview' %}

    <!-- Browse Listings -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_browse_listings' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>' label='Browse Listings' description='Discover properties' item_key='browse' %}

    <!-- Favorites -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_favorites' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path></svg>' label='Favorites' description='Saved properties' item_key='favorites' %}

    <!-- My Inquiries -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_inquiries' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>' label='My Inquiries' description='Track communications' item_key='inquiries' %}

    <!-- Saved Searches -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_saved_searches' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path></svg>' label='Saved Searches' description='Search alerts' item_key='searches' %}

    <!-- Market Insights -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_market_insights' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Market Insights' description='Trends & analytics' item_key='insights' %}

    <!-- Divider -->
    <div class="my-6">
        <div class="h-px bg-gradient-to-r from-transparent via-slate-600 to-transparent"></div>
    </div>

    <!-- Buying Guide -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_buying_guide' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>' label='Buying Guide' description='Tips & resources' item_key='guide' %}

    <!-- Help & Support -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_support' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>' label='Support' description='Help & documentation' item_key='support' %}
</div>
{% endblock %}

{% block page_title %}Land Buying Guide{% endblock %}
{% block page_description %}Essential tips and resources for land buyers{% endblock %}

{% block page_actions %}
<a href="{% url 'buyer_browse_listings' %}" class="btn btn-primary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
    </svg>
    Start Browsing
</a>
{% endblock %}

{% block dashboard_content %}
<!-- Introduction -->
<div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 mb-8 border border-blue-100">
    <div class="max-w-3xl">
        <h2 class="text-2xl font-bold text-secondary-900 mb-4">Welcome to the Land Buying Guide</h2>
        <p class="text-lg text-secondary-700 mb-6">
            Buying land is a significant investment that requires careful planning and research. This comprehensive guide will walk you through the essential steps and considerations to help you make informed decisions.
        </p>
        <div class="flex items-center space-x-4">
            <div class="flex items-center text-sm text-secondary-600">
                <svg class="w-5 h-5 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                Expert advice
            </div>
            <div class="flex items-center text-sm text-secondary-600">
                <svg class="w-5 h-5 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                Step-by-step process
            </div>
            <div class="flex items-center text-sm text-secondary-600">
                <svg class="w-5 h-5 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                Practical tips
            </div>
        </div>
    </div>
</div>

<!-- Guide Sections -->
<div class="space-y-8">
    {% for section in guide_sections %}
        <div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden">
            <div class="px-8 py-6 border-b border-secondary-100 bg-gradient-to-r from-secondary-50/50 to-transparent">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white font-bold text-lg">
                        {{ forloop.counter }}
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-secondary-900">{{ section.title }}</h3>
                        <p class="text-sm text-secondary-600 mt-1">{{ section.description }}</p>
                    </div>
                </div>
            </div>
            <div class="p-8">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {% for topic in section.topics %}
                        <div class="flex items-start space-x-3 p-4 bg-secondary-50 rounded-xl hover:bg-secondary-100 transition-colors duration-200">
                            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-secondary-900">{{ topic }}</p>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endfor %}
</div>

<!-- Quick Tips -->
<div class="bg-white rounded-2xl shadow-sm border border-secondary-100 overflow-hidden mt-8">
    <div class="px-8 py-6 border-b border-secondary-100 bg-gradient-to-r from-amber-50/50 to-transparent">
        <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-xl font-bold text-secondary-900">Quick Tips for Success</h3>
                <p class="text-sm text-secondary-600 mt-1">Essential reminders for land buyers</p>
            </div>
        </div>
    </div>
    <div class="p-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="text-center p-6 bg-blue-50 rounded-xl">
                <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h4 class="font-semibold text-secondary-900 mb-2">Do Your Research</h4>
                <p class="text-sm text-secondary-600">Thoroughly investigate the property, location, and market conditions before making an offer.</p>
            </div>
            
            <div class="text-center p-6 bg-green-50 rounded-xl">
                <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <h4 class="font-semibold text-secondary-900 mb-2">Budget Wisely</h4>
                <p class="text-sm text-secondary-600">Factor in all costs including surveys, inspections, and potential development expenses.</p>
            </div>
            
            <div class="text-center p-6 bg-purple-50 rounded-xl">
                <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h4 class="font-semibold text-secondary-900 mb-2">Get Professional Help</h4>
                <p class="text-sm text-secondary-600">Work with experienced real estate agents, lawyers, and surveyors for guidance.</p>
            </div>
        </div>
    </div>
</div>

<!-- Call to Action -->
<div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-center text-white mt-8">
    <h3 class="text-2xl font-bold mb-4">Ready to Start Your Land Search?</h3>
    <p class="text-blue-100 mb-6 max-w-2xl mx-auto">
        Now that you're equipped with the knowledge, browse our available properties and find the perfect piece of land for your needs.
    </p>
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="{% url 'buyer_browse_listings' %}" class="btn bg-white text-blue-600 hover:bg-blue-50 btn-lg">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            Browse Properties
        </a>
        <a href="{% url 'buyer_market_insights' %}" class="btn btn-outline border-white text-white hover:bg-white hover:text-blue-600 btn-lg">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            View Market Insights
        </a>
    </div>
</div>
{% endblock %}
