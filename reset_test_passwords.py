#!/usr/bin/env python
"""
Quick script to reset all test account passwords to their default values.
Useful if passwords get changed during testing.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LandHub.settings')
django.setup()

from django.contrib.auth.models import User

def reset_passwords():
    """Reset all test account passwords to their defaults"""
    
    print("🔄 Resetting LandHub Test Account Passwords...")
    print("=" * 50)
    
    # Password mappings
    password_map = {
        'admin': 'admin123',
        'superadmin': 'admin123',
        'seller_john': 'seller123',
        'seller_sarah': 'seller123',
        'seller_mike': 'seller123',
        'seller_lisa': 'seller123',
        'seller_demo': 'seller123',
        'buyer_alex': 'buyer123',
        'buyer_emma': 'buyer123',
        'buyer_david': 'buyer123',
        'buyer_jennifer': 'buyer123',
        'buyer_robert': 'buyer123',
        'buyer_maria': 'buyer123',
        'buyer_demo': 'buyer123',
    }
    
    success_count = 0
    
    for username, password in password_map.items():
        try:
            user = User.objects.get(username=username)
            user.set_password(password)
            user.is_active = True  # Ensure user is active
            user.save()
            print(f"✅ {username}: Password reset to '{password}'")
            success_count += 1
        except User.DoesNotExist:
            print(f"❌ {username}: User not found")
        except Exception as e:
            print(f"❌ {username}: Error - {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 Reset Results: {success_count}/{len(password_map)} passwords reset successfully")
    
    if success_count == len(password_map):
        print("\n🎉 All test account passwords have been reset!")
        print("📋 See README_ACCOUNTS.md for login credentials.")
    else:
        print(f"\n⚠️  {len(password_map) - success_count} accounts need attention.")

if __name__ == "__main__":
    try:
        reset_passwords()
    except Exception as e:
        print(f"❌ Password reset failed with error: {str(e)}")
        sys.exit(1)
