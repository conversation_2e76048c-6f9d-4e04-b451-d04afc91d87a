from django import forms
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.contrib.auth.models import User
from .models import UserProfile, Land, LandImage, Inquiry


class MultipleFileInput(forms.ClearableFileInput):
    allow_multiple_selected = True


class MultipleFileField(forms.FileField):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault("widget", MultipleFileInput())
        super().__init__(*args, **kwargs)

    def clean(self, data, initial=None):
        single_file_clean = super().clean
        if isinstance(data, (list, tuple)):
            result = [single_file_clean(d, initial) for d in data]
        else:
            result = single_file_clean(data, initial)
        return result


class CustomAuthenticationForm(AuthenticationForm):
    """Custom login form with enhanced styling"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Add CSS classes and placeholders
        self.fields['username'].widget.attrs.update({
            'class': 'form-input',
            'placeholder': 'Enter your username or email'
        })
        self.fields['password'].widget.attrs.update({
            'class': 'form-input',
            'placeholder': 'Enter your password'
        })


class CustomUserCreationForm(UserCreationForm):
    email = forms.EmailField(required=True)
    first_name = forms.CharField(max_length=30, required=True)
    last_name = forms.CharField(max_length=30, required=True)
    role = forms.ChoiceField(choices=UserProfile.ROLE_CHOICES, initial='buyer')
    phone = forms.CharField(max_length=15, required=False)
    
    class Meta:
        model = User
        fields = ('username', 'first_name', 'last_name', 'email', 'password1', 'password2')
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add Tailwind CSS classes
        for field_name, field in self.fields.items():
            if field_name == 'role':
                field.widget.attrs.update({
                    'class': 'form-select'
                })
            else:
                field.widget.attrs.update({
                    'class': 'form-input'
                })

        # Add specific placeholders
        self.fields['first_name'].widget.attrs.update({
            'placeholder': 'Enter your first name'
        })
        self.fields['last_name'].widget.attrs.update({
            'placeholder': 'Enter your last name'
        })
        self.fields['username'].widget.attrs.update({
            'placeholder': 'Choose a username'
        })
        self.fields['email'].widget.attrs.update({
            'placeholder': 'Enter your email address'
        })
        self.fields['phone'].widget.attrs.update({
            'placeholder': 'Enter your phone number (optional)'
        })
        self.fields['password1'].widget.attrs.update({
            'placeholder': 'Create a strong password'
        })
        self.fields['password2'].widget.attrs.update({
            'placeholder': 'Confirm your password'
        })
    
    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        
        if commit:
            user.save()
            # Update the user profile with role and phone
            profile = user.profile
            profile.role = self.cleaned_data['role']
            profile.phone = self.cleaned_data.get('phone', '')
            profile.save()
        
        return user


class UserProfileForm(forms.ModelForm):
    first_name = forms.CharField(max_length=30, required=True)
    last_name = forms.CharField(max_length=30, required=True)
    email = forms.EmailField(required=True)
    
    class Meta:
        model = UserProfile
        fields = ['phone', 'bio', 'avatar']
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.user:
            self.fields['first_name'].initial = self.instance.user.first_name
            self.fields['last_name'].initial = self.instance.user.last_name
            self.fields['email'].initial = self.instance.user.email
        
        # Add Tailwind CSS classes
        for field_name, field in self.fields.items():
            if field_name == 'bio':
                field.widget.attrs.update({
                    'class': 'form-textarea',
                    'rows': 4,
                    'placeholder': 'Tell others about yourself and your interests in land'
                })
            elif field_name == 'avatar':
                field.widget.attrs.update({
                    'class': 'form-input',
                    'accept': 'image/*'
                })
            else:
                field.widget.attrs.update({
                    'class': 'form-input'
                })

        # Add specific placeholders
        self.fields['first_name'].widget.attrs.update({
            'placeholder': 'Enter your first name'
        })
        self.fields['last_name'].widget.attrs.update({
            'placeholder': 'Enter your last name'
        })
        self.fields['email'].widget.attrs.update({
            'placeholder': 'Enter your email address'
        })
        self.fields['phone'].widget.attrs.update({
            'placeholder': 'Enter your phone number'
        })
    
    def save(self, commit=True):
        profile = super().save(commit=False)
        if commit:
            # Update User model fields
            user = profile.user
            user.first_name = self.cleaned_data['first_name']
            user.last_name = self.cleaned_data['last_name']
            user.email = self.cleaned_data['email']
            user.save()
            profile.save()
        return profile

class LandListingForm(forms.ModelForm):
    """Form for creating and editing land listings"""
    
    class Meta:
        model = Land
        fields = [
            'title', 'description', 'price', 'size_acres', 'location', 
            'address', 'property_type'
        ]
        widgets = {
            'description': forms.Textarea(attrs={'rows': 6}),
            'address': forms.Textarea(attrs={'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add Tailwind CSS classes and placeholders
        self.fields['title'].widget.attrs.update({
            'class': 'form-input',
            'placeholder': 'Enter a descriptive title for your land listing'
        })
        
        self.fields['description'].widget.attrs.update({
            'class': 'form-textarea',
            'placeholder': 'Describe your land in detail - features, potential uses, nearby amenities, etc.'
        })
        
        self.fields['price'].widget.attrs.update({
            'class': 'form-input',
            'placeholder': '0.00',
            'step': '0.01',
            'min': '0'
        })
        
        self.fields['size_acres'].widget.attrs.update({
            'class': 'form-input',
            'placeholder': '0.00',
            'step': '0.01',
            'min': '0.01'
        })
        
        self.fields['location'].widget.attrs.update({
            'class': 'form-input',
            'placeholder': 'City, State or general area'
        })
        
        self.fields['address'].widget.attrs.update({
            'class': 'form-textarea',
            'placeholder': 'Full address or detailed location information'
        })
        
        self.fields['property_type'].widget.attrs.update({
            'class': 'form-select'
        })
        
        # Add labels
        self.fields['title'].label = 'Listing Title'
        self.fields['description'].label = 'Property Description'
        self.fields['price'].label = 'Price ($)'
        self.fields['size_acres'].label = 'Size (Acres)'
        self.fields['location'].label = 'Location'
        self.fields['address'].label = 'Address'
        self.fields['property_type'].label = 'Property Type'


class LandImageForm(forms.ModelForm):
    """Form for uploading land images"""
    
    class Meta:
        model = LandImage
        fields = ['image', 'alt_text', 'is_primary']
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.fields['image'].widget.attrs.update({
            'class': 'form-input',
            'accept': 'image/*'
        })
        
        self.fields['alt_text'].widget.attrs.update({
            'class': 'form-input',
            'placeholder': 'Brief description of the image'
        })
        
        self.fields['is_primary'].widget.attrs.update({
            'class': 'form-checkbox'
        })


class MultipleImageUploadForm(forms.Form):
    """Form for handling multiple image uploads"""
    images = MultipleFileField(
        widget=MultipleFileInput(attrs={
            'accept': 'image/*',
            'class': 'form-input'
        }),
        required=False
    )
    
    def clean_images(self):
        images = self.files.getlist('images')
        
        if len(images) > 10:
            raise forms.ValidationError('You can upload a maximum of 10 images.')
        
        for image in images:
            if image.size > 5 * 1024 * 1024:  # 5MB limit
                raise forms.ValidationError(f'Image {image.name} is too large. Maximum size is 5MB.')
            
            if not image.content_type.startswith('image/'):
                raise forms.ValidationError(f'File {image.name} is not a valid image.')
        
        return images


class InquiryResponseForm(forms.ModelForm):
    """Form for sellers to respond to inquiries"""
    
    class Meta:
        model = Inquiry
        fields = ['seller_response']
        widgets = {
            'seller_response': forms.Textarea(attrs={'rows': 4})
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.fields['seller_response'].widget.attrs.update({
            'class': 'form-textarea',
            'placeholder': 'Type your response to the buyer...'
        })
        
        self.fields['seller_response'].label = 'Your Response'


class InquiryForm(forms.ModelForm):
    """Form for buyers to send inquiries"""
    
    class Meta:
        model = Inquiry
        fields = ['subject', 'message']
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.fields['subject'].widget.attrs.update({
            'class': 'form-input',
            'placeholder': 'What would you like to know about this property?'
        })
        
        self.fields['message'].widget.attrs.update({
            'class': 'form-textarea',
            'placeholder': 'Please provide details about your inquiry...',
            'rows': 4
        })