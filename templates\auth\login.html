{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Sign In - LandHub{% endblock %}

{% block main_class %}flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-primary-50 via-white to-secondary-50{% endblock %}

{% block content %}
<div class="w-full lg:grid lg:grid-cols-2 min-h-screen">
    <!-- Branding/Info Section -->
    <div class="hidden lg:flex flex-col items-center justify-center p-12 bg-gradient-to-br from-primary-700 to-primary-900 text-white relative overflow-hidden">
        <div class="absolute top-0 left-0 w-full h-full bg-cover bg-center opacity-10" style="background-image: url('https://images.unsplash.com/photo-1582407947304-fd86f028f716?auto=format&fit=crop&w=1973&q=80');"></div>
        <div class="relative z-10 text-center space-y-6">
            <a href="{% url 'home' %}" class="inline-block bg-white/20 p-4 rounded-2xl backdrop-blur-sm">
                <svg class="w-16 h-16 text-white" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
            </a>
            <h1 class="text-4xl xl:text-5xl font-display font-bold tracking-tight">Unlock Your Dream Property</h1>
            <p class="text-lg text-primary-200 max-w-md mx-auto">
                Join thousands of users finding, buying, and selling land with ease on the most trusted platform.
            </p>
        </div>
        <div class="absolute bottom-8 left-8 right-8 z-10 text-center text-sm text-primary-300">
            <p>&copy; {% now "Y" %} LandHub. All rights reserved.</p>
        </div>
    </div>

    <!-- Form Section -->
    <div class="flex flex-col items-center justify-center p-6 sm:p-12 w-full">
        <div class="w-full max-w-md space-y-6">
            <!-- Mobile Header -->
            <div class="text-center lg:hidden">
                <a href="{% url 'home' %}" class="inline-block text-primary-600 bg-primary-100 p-3 rounded-2xl">
                    <svg class="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                </a>
                <h1 class="text-3xl font-display font-bold text-secondary-900 mt-4">Welcome Back</h1>
                <p class="text-secondary-600">Sign in to continue to LandHub.</p>
            </div>

            <!-- Login Form -->
            <div class="bg-white p-8 rounded-2xl shadow-lg border border-secondary-100">
                <div class="text-center mb-8 hidden lg:block">
                    <h1 class="text-3xl font-display font-bold text-secondary-900">Sign In</h1>
                    <p class="text-secondary-600">Enter your credentials to access your account.</p>
                </div>

                <form method="post" action="{% url 'auth:login' %}" class="space-y-6">
                    {% csrf_token %}

                    <!-- Username Field -->
                    <div>
                        <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700">Username</label>
                        {{ form.username|add_class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"|add_attr:"placeholder:<EMAIL>" }}
                        {% for error in form.username.errors %}
                            <p class="form-error">{{ error }}</p>
                        {% endfor %}
                    </div>

                    <!-- Password Field -->
                    <div>
                        <label for="{{ form.password.id_for_label }}" class="block text-sm font-medium text-gray-700">Password</label>
                        {{ form.password|add_class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"|add_attr:"placeholder:Enter your password" }}
                        {% for error in form.password.errors %}
                            <p class="form-error">{{ error }}</p>
                        {% endfor %}
                    </div>

                    <!-- Remember Me & Forgot Password -->
                    <div class="flex items-center justify-between text-sm">
                        <div class="flex items-center">
                            <input id="remember-me" name="remember-me" type="checkbox" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded">
                            <label for="remember-me" class="ml-2 block text-secondary-700">Remember me</label>
                        </div>
                        <a href="{% url 'auth:password_reset' %}" class="font-medium text-primary-600 hover:text-primary-800 hover:underline">Forgot password?</a>
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
                            Sign In
                        </button>
                    </div>
                </form>
            </div>

            <!-- Sign Up Link -->
            <p class="text-center text-sm text-secondary-600">
                Don't have an account?
                <a href="{% url 'auth:register' %}" class="font-semibold text-primary-600 hover:text-primary-800 hover:underline">Sign up for free</a>
            </p>
        </div>
    </div>
</div>
{% endblock %}