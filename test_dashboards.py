#!/usr/bin/env python
"""
Test script to verify all dashboards are working correctly.
This script will test the dashboard URLs and check for template errors.
"""

import os
import sys
import django
import requests
from django.test import Client
from django.contrib.auth import authenticate

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LandHub.settings')
django.setup()

from django.contrib.auth.models import User
from landmarket.models import UserProfile

def test_dashboard_access():
    """Test dashboard access for all user roles"""
    
    print("🧪 Testing LandHub Dashboard Access...")
    print("=" * 50)
    
    # Create a test client
    client = Client()
    
    # Test accounts
    test_accounts = [
        {'username': 'admin', 'password': 'admin123', 'role': 'admin', 'dashboard': '/admin-dashboard/'},
        {'username': 'seller_demo', 'password': 'seller123', 'role': 'seller', 'dashboard': '/seller-dashboard/'},
        {'username': 'buyer_demo', 'password': 'buyer123', 'role': 'buyer', 'dashboard': '/buyer-dashboard/'},
    ]
    
    success_count = 0
    total_tests = len(test_accounts)
    
    for account in test_accounts:
        username = account['username']
        password = account['password']
        role = account['role']
        dashboard_url = account['dashboard']
        
        try:
            # Test login
            login_success = client.login(username=username, password=password)
            
            if not login_success:
                print(f"❌ {role.title()} ({username}): Login failed")
                continue
            
            # Test dashboard access
            response = client.get(dashboard_url)
            
            if response.status_code == 200:
                print(f"✅ {role.title()} ({username}): Dashboard accessible (Status: {response.status_code})")
                success_count += 1
            elif response.status_code == 302:
                print(f"⚠️  {role.title()} ({username}): Redirected (Status: {response.status_code})")
                print(f"   → Redirect location: {response.get('Location', 'Unknown')}")
            else:
                print(f"❌ {role.title()} ({username}): Dashboard error (Status: {response.status_code})")
                
            # Logout for next test
            client.logout()
            
        except Exception as e:
            print(f"❌ {role.title()} ({username}): Error - {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 Dashboard Test Results: {success_count}/{total_tests} dashboards working correctly")
    
    return success_count == total_tests

def test_template_syntax():
    """Test for template syntax errors by checking specific pages"""
    
    print("\n🔍 Testing Template Syntax...")
    print("=" * 30)
    
    client = Client()
    
    # Test pages that don't require authentication
    public_pages = [
        {'url': '/', 'name': 'Homepage'},
        {'url': '/auth/login/', 'name': 'Login Page'},
        {'url': '/auth/register/', 'name': 'Registration Page'},
    ]
    
    success_count = 0
    
    for page in public_pages:
        try:
            response = client.get(page['url'])
            if response.status_code == 200:
                print(f"✅ {page['name']}: Template renders correctly")
                success_count += 1
            else:
                print(f"❌ {page['name']}: Status {response.status_code}")
        except Exception as e:
            print(f"❌ {page['name']}: Template error - {str(e)}")
    
    print(f"\n📊 Template Test Results: {success_count}/{len(public_pages)} templates working")
    
    return success_count == len(public_pages)

def test_custom_filters():
    """Test custom template filters"""
    
    print("\n🔧 Testing Custom Template Filters...")
    print("=" * 35)
    
    try:
        from landmarket.templatetags.form_tags import div
        
        # Test div filter
        result = div(100, 5)
        if result == 20:
            print("✅ div filter: Working correctly (100 ÷ 5 = 20)")
        else:
            print(f"❌ div filter: Incorrect result (got {result}, expected 20)")
            return False
            
        # Test division by zero
        result = div(100, 0)
        if result == 0:
            print("✅ div filter: Division by zero handled correctly")
        else:
            print(f"❌ div filter: Division by zero not handled (got {result}, expected 0)")
            return False
            
        print("📊 Custom Filter Test: All filters working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Custom Filter Test: Error - {str(e)}")
        return False

def main():
    """Run all tests"""
    
    print("🚀 LandHub Dashboard Testing Suite")
    print("=" * 60)
    
    # Run tests
    dashboard_test = test_dashboard_access()
    template_test = test_template_syntax()
    filter_test = test_custom_filters()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 FINAL TEST SUMMARY")
    print("=" * 60)
    
    tests = [
        ("Dashboard Access", dashboard_test),
        ("Template Syntax", template_test),
        ("Custom Filters", filter_test),
    ]
    
    passed_tests = sum(1 for _, result in tests if result)
    total_tests = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall Result: {passed_tests}/{total_tests} test suites passed")
    
    if passed_tests == total_tests:
        print("\n🎉 All tests passed! LandHub dashboards are working correctly.")
        print("🌐 You can now access:")
        print("   • Admin Dashboard: http://127.0.0.1:8000/admin-dashboard/")
        print("   • Seller Dashboard: http://127.0.0.1:8000/seller-dashboard/")
        print("   • Buyer Dashboard: http://127.0.0.1:8000/buyer-dashboard/")
        print("   • Login Page: http://127.0.0.1:8000/auth/login/")
        return True
    else:
        print(f"\n⚠️  {total_tests - passed_tests} test suite(s) failed. Check the errors above.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test suite failed with error: {str(e)}")
        sys.exit(1)
