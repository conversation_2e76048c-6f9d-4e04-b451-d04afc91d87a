#!/usr/bin/env python
"""
Quick verification script to check that all test accounts are properly created
and can authenticate successfully.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LandHub.settings')
django.setup()

from django.contrib.auth.models import User
from django.contrib.auth import authenticate
from landmarket.models import UserProfile, Land, Inquiry, Favorite

def verify_accounts():
    """Verify all test accounts exist and can authenticate"""
    
    print("🔍 Verifying LandHub Test Accounts...")
    print("=" * 50)
    
    # Test accounts data
    test_accounts = [
        # Admin accounts
        {'username': 'admin', 'password': 'admin123', 'role': 'admin'},
        {'username': 'superadmin', 'password': 'admin123', 'role': 'admin'},
        
        # Seller accounts
        {'username': 'seller_john', 'password': 'seller123', 'role': 'seller'},
        {'username': 'seller_sarah', 'password': 'seller123', 'role': 'seller'},
        {'username': 'seller_mike', 'password': 'seller123', 'role': 'seller'},
        {'username': 'seller_lisa', 'password': 'seller123', 'role': 'seller'},
        {'username': 'seller_demo', 'password': 'seller123', 'role': 'seller'},
        
        # Buyer accounts
        {'username': 'buyer_alex', 'password': 'buyer123', 'role': 'buyer'},
        {'username': 'buyer_emma', 'password': 'buyer123', 'role': 'buyer'},
        {'username': 'buyer_david', 'password': 'buyer123', 'role': 'buyer'},
        {'username': 'buyer_jennifer', 'password': 'buyer123', 'role': 'buyer'},
        {'username': 'buyer_robert', 'password': 'buyer123', 'role': 'buyer'},
        {'username': 'buyer_maria', 'password': 'buyer123', 'role': 'buyer'},
        {'username': 'buyer_demo', 'password': 'buyer123', 'role': 'buyer'},
    ]
    
    success_count = 0
    total_count = len(test_accounts)
    
    for account in test_accounts:
        username = account['username']
        password = account['password']
        expected_role = account['role']
        
        try:
            # Check if user exists
            user = User.objects.get(username=username)
            
            # Test authentication
            auth_user = authenticate(username=username, password=password)
            
            if auth_user is None:
                print(f"❌ {username}: Authentication failed")
                continue
            
            # Check role
            if hasattr(user, 'profile') and user.profile.role == expected_role:
                print(f"✅ {username}: OK (Role: {user.profile.role})")
                success_count += 1
            else:
                print(f"⚠️  {username}: Role mismatch (Expected: {expected_role}, Got: {user.profile.role if hasattr(user, 'profile') else 'No profile'})")
                
        except User.DoesNotExist:
            print(f"❌ {username}: User does not exist")
        except Exception as e:
            print(f"❌ {username}: Error - {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 Verification Results: {success_count}/{total_count} accounts verified successfully")
    
    # Additional statistics
    print("\n📈 Platform Statistics:")
    print(f"   👥 Total Users: {User.objects.count()}")
    print(f"   👑 Admins: {UserProfile.objects.filter(role='admin').count()}")
    print(f"   🏢 Sellers: {UserProfile.objects.filter(role='seller').count()}")
    print(f"   🏠 Buyers: {UserProfile.objects.filter(role='buyer').count()}")
    print(f"   🏞️  Land Listings: {Land.objects.count()}")
    print(f"   💬 Inquiries: {Inquiry.objects.count()}")
    print(f"   ❤️  Favorites: {Favorite.objects.count()}")
    
    if success_count == total_count:
        print("\n🎉 All test accounts are working correctly!")
        print("📋 See README_ACCOUNTS.md for login credentials.")
        return True
    else:
        print(f"\n⚠️  {total_count - success_count} accounts need attention.")
        return False

def show_dashboard_urls():
    """Show the dashboard URLs for each role"""
    print("\n🌐 Dashboard URLs:")
    print("   👑 Admin Dashboard: /admin-dashboard/")
    print("   🏢 Seller Dashboard: /seller-dashboard/")
    print("   🏠 Buyer Dashboard: /buyer-dashboard/")
    print("   🔐 Login Page: /auth/login/")

if __name__ == "__main__":
    try:
        success = verify_accounts()
        show_dashboard_urls()
        
        if success:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Verification failed with error: {str(e)}")
        sys.exit(1)
