from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from landmarket.models import UserProfile, Land, Inquiry, Favorite
from decimal import Decimal
import random


class Command(BaseCommand):
    help = 'Create comprehensive test user accounts and sample data for LandHub platform'

    def add_arguments(self, parser):
        parser.add_argument(
            '--users-only',
            action='store_true',
            help='Create only user accounts without sample data',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating LandHub test accounts...'))

        # Create admin users
        admin_users = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'first_name': 'System',
                'last_name': 'Administrator',
                'phone': '******-0100',
                'bio': 'Platform administrator with full system access.'
            },
            {
                'username': 'superadmin',
                'email': '<EMAIL>',
                'first_name': 'Super',
                'last_name': 'Admin',
                'phone': '******-0101',
                'bio': 'Super administrator for platform management.'
            }
        ]

        for admin_data in admin_users:
            admin_user, created = User.objects.get_or_create(
                username=admin_data['username'],
                defaults={
                    'email': admin_data['email'],
                    'first_name': admin_data['first_name'],
                    'last_name': admin_data['last_name'],
                    'is_staff': True,
                    'is_superuser': True
                }
            )
            if created:
                admin_user.set_password('admin123')
                admin_user.save()
                admin_user.profile.role = 'admin'
                admin_user.profile.phone = admin_data['phone']
                admin_user.profile.bio = admin_data['bio']
                admin_user.profile.save()
                self.stdout.write(f'✓ Created admin user: {admin_user.username}')
            else:
                self.stdout.write(f'→ Admin user already exists: {admin_user.username}')

        # Create seller users with realistic data
        seller_data = [
            {
                'username': 'seller_john',
                'email': '<EMAIL>',
                'first_name': 'John',
                'last_name': 'Smith',
                'phone': '******-0201',
                'bio': 'Experienced land broker specializing in agricultural properties in Texas.'
            },
            {
                'username': 'seller_sarah',
                'email': '<EMAIL>',
                'first_name': 'Sarah',
                'last_name': 'Johnson',
                'phone': '******-0202',
                'bio': 'Real estate professional focused on recreational and residential land sales.'
            },
            {
                'username': 'seller_mike',
                'email': '<EMAIL>',
                'first_name': 'Mike',
                'last_name': 'Davis',
                'phone': '******-0203',
                'bio': 'Commercial land specialist with 15+ years of experience.'
            },
            {
                'username': 'seller_lisa',
                'email': '<EMAIL>',
                'first_name': 'Lisa',
                'last_name': 'Wilson',
                'phone': '******-0204',
                'bio': 'Family-owned ranch specialist helping preserve Texas heritage.'
            },
            {
                'username': 'seller_demo',
                'email': '<EMAIL>',
                'first_name': 'Demo',
                'last_name': 'Seller',
                'phone': '******-0205',
                'bio': 'Demo seller account for testing platform features.'
            }
        ]

        sellers = []
        for seller_info in seller_data:
            seller, created = User.objects.get_or_create(
                username=seller_info['username'],
                defaults={
                    'email': seller_info['email'],
                    'first_name': seller_info['first_name'],
                    'last_name': seller_info['last_name']
                }
            )
            if created:
                seller.set_password('seller123')
                seller.save()
                seller.profile.role = 'seller'
                seller.profile.phone = seller_info['phone']
                seller.profile.bio = seller_info['bio']
                seller.profile.save()
                sellers.append(seller)
                self.stdout.write(f'✓ Created seller: {seller.username}')
            else:
                sellers.append(seller)
                self.stdout.write(f'→ Seller already exists: {seller.username}')

        # Create buyer users with realistic data
        buyer_data = [
            {
                'username': 'buyer_alex',
                'email': '<EMAIL>',
                'first_name': 'Alex',
                'last_name': 'Brown',
                'phone': '******-0301',
                'bio': 'Looking for agricultural land to start organic farming operation.'
            },
            {
                'username': 'buyer_emma',
                'email': '<EMAIL>',
                'first_name': 'Emma',
                'last_name': 'Taylor',
                'phone': '******-0302',
                'bio': 'Seeking recreational property for family retreat and hunting.'
            },
            {
                'username': 'buyer_david',
                'email': '<EMAIL>',
                'first_name': 'David',
                'last_name': 'Martinez',
                'phone': '******-0303',
                'bio': 'Investment buyer interested in commercial development opportunities.'
            },
            {
                'username': 'buyer_jennifer',
                'email': '<EMAIL>',
                'first_name': 'Jennifer',
                'last_name': 'Garcia',
                'phone': '******-0304',
                'bio': 'First-time land buyer looking for residential building lots.'
            },
            {
                'username': 'buyer_robert',
                'email': '<EMAIL>',
                'first_name': 'Robert',
                'last_name': 'Lee',
                'phone': '******-0305',
                'bio': 'Rancher expanding operations, seeking grazing land.'
            },
            {
                'username': 'buyer_maria',
                'email': '<EMAIL>',
                'first_name': 'Maria',
                'last_name': 'Rodriguez',
                'phone': '******-0306',
                'bio': 'Environmental consultant looking for conservation land.'
            },
            {
                'username': 'buyer_demo',
                'email': '<EMAIL>',
                'first_name': 'Demo',
                'last_name': 'Buyer',
                'phone': '******-0307',
                'bio': 'Demo buyer account for testing platform features.'
            }
        ]

        buyers = []
        for buyer_info in buyer_data:
            buyer, created = User.objects.get_or_create(
                username=buyer_info['username'],
                defaults={
                    'email': buyer_info['email'],
                    'first_name': buyer_info['first_name'],
                    'last_name': buyer_info['last_name']
                }
            )
            if created:
                buyer.set_password('buyer123')
                buyer.save()
                buyer.profile.role = 'buyer'
                buyer.profile.phone = buyer_info['phone']
                buyer.profile.bio = buyer_info['bio']
                buyer.profile.save()
                buyers.append(buyer)
                self.stdout.write(f'✓ Created buyer: {buyer.username}')
            else:
                buyers.append(buyer)
                self.stdout.write(f'→ Buyer already exists: {buyer.username}')

        # Skip sample data creation if --users-only flag is used
        if options['users_only']:
            self.stdout.write(self.style.SUCCESS('\n✅ User accounts created successfully!'))
            self.stdout.write(self.style.WARNING('Use --help to see all available options.'))
            return

        # Create sample land listings with realistic data
        self.stdout.write('\nCreating sample land listings...')

        property_types = ['residential', 'commercial', 'agricultural', 'recreational']
        statuses = ['pending', 'approved', 'rejected', 'draft']
        locations = [
            'Austin, TX', 'Dallas, TX', 'Houston, TX', 'San Antonio, TX', 'Fort Worth, TX',
            'El Paso, TX', 'Arlington, TX', 'Corpus Christi, TX', 'Plano, TX', 'Lubbock, TX'
        ]

        sample_listings = [
            {
                'title': 'Prime Agricultural Land - 50 Acres',
                'description': 'Excellent farmland with rich soil, perfect for crop production. Includes irrigation system and barn.',
                'price': 125000,
                'size_acres': 50,
                'property_type': 'agricultural'
            },
            {
                'title': 'Residential Building Lots - Gated Community',
                'description': 'Beautiful residential lots in exclusive gated community. Utilities available, ready to build.',
                'price': 85000,
                'size_acres': 2.5,
                'property_type': 'residential'
            },
            {
                'title': 'Commercial Development Opportunity',
                'description': 'Prime commercial land on major highway. Perfect for retail or office development.',
                'price': 450000,
                'size_acres': 8,
                'property_type': 'commercial'
            },
            {
                'title': 'Hunting Ranch - 200 Acres',
                'description': 'Excellent hunting property with diverse wildlife. Includes cabin and feeders.',
                'price': 320000,
                'size_acres': 200,
                'property_type': 'recreational'
            },
            {
                'title': 'Waterfront Property - Lake Access',
                'description': 'Beautiful lakefront property with private dock. Perfect for vacation home or investment.',
                'price': 275000,
                'size_acres': 5,
                'property_type': 'recreational'
            }
        ]

        for i, listing_data in enumerate(sample_listings):
            if sellers:
                seller = sellers[i % len(sellers)]
                land, created = Land.objects.get_or_create(
                    title=listing_data['title'],
                    defaults={
                        'owner': seller,
                        'description': listing_data['description'],
                        'price': Decimal(str(listing_data['price'])),
                        'size_acres': Decimal(str(listing_data['size_acres'])),
                        'location': random.choice(locations),
                        'address': f'{random.randint(100, 9999)} County Road {random.randint(1, 999)}',
                        'property_type': listing_data['property_type'],
                        'status': random.choice(statuses),
                        'is_approved': random.choice([True, False])
                    }
                )
                if created:
                    self.stdout.write(f'✓ Created listing: {listing_data["title"]}')

        # Create additional random listings
        for i in range(15):
            if sellers:
                seller = random.choice(sellers)
                land, created = Land.objects.get_or_create(
                    title=f'Land Parcel #{i+6:03d}',
                    defaults={
                        'owner': seller,
                        'description': f'Quality {random.choice(property_types)} land with great potential for development or investment.',
                        'price': Decimal(str(random.randint(25000, 750000))),
                        'size_acres': Decimal(str(random.randint(1, 150))),
                        'location': random.choice(locations),
                        'address': f'{random.randint(100, 9999)} County Road {random.randint(1, 999)}',
                        'property_type': random.choice(property_types),
                        'status': random.choice(statuses),
                        'is_approved': random.choice([True, False])
                    }
                )
                if created:
                    self.stdout.write(f'✓ Created listing: Land Parcel #{i+6:03d}')

        # Create sample inquiries
        self.stdout.write('\nCreating sample inquiries...')
        lands = Land.objects.all()
        inquiry_messages = [
            "Hi, I'm very interested in this property. Could you provide more details about the soil quality and water rights?",
            "This looks perfect for my needs. Is the property currently under any agricultural exemptions?",
            "I'd like to schedule a viewing. What's the best time to visit the property?",
            "Can you tell me more about the zoning restrictions and building permits for this land?",
            "I'm interested in making an offer. What's the timeline for closing?",
            "Does this property come with mineral rights? Also, are there any easements I should know about?",
            "This property caught my eye. Can you provide a survey and recent appraisal?",
            "I'm looking to purchase for investment purposes. What's the potential for subdivision?"
        ]

        for i in range(25):
            if buyers and lands:
                buyer = random.choice(buyers)
                land = random.choice(lands)
                inquiry, created = Inquiry.objects.get_or_create(
                    buyer=buyer,
                    land=land,
                    subject=f'Inquiry about {land.title}',
                    defaults={
                        'message': random.choice(inquiry_messages),
                        'is_read': random.choice([True, False])
                    }
                )
                if created:
                    self.stdout.write(f'✓ Created inquiry from {buyer.username} for {land.title}')

        # Create sample favorites
        self.stdout.write('\nCreating sample favorites...')
        for i in range(20):
            if buyers and lands:
                buyer = random.choice(buyers)
                land = random.choice(lands)
                favorite, created = Favorite.objects.get_or_create(
                    user=buyer,
                    land=land
                )
                if created:
                    self.stdout.write(f'✓ Created favorite: {buyer.username} → {land.title}')

        self.stdout.write(self.style.SUCCESS('\n🎉 Sample data created successfully!'))
        self.stdout.write(self.style.WARNING('\n📋 Check the README_ACCOUNTS.md file for login credentials.'))