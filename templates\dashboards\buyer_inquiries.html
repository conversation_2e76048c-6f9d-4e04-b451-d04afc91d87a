{% extends 'dashboards/base_dashboard.html' %}

{% block dashboard_title %}My Inquiries{% endblock %}
{% block active_nav_item %}inquiries{% endblock %}

{% block sidebar_icon %}
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
</svg>
{% endblock %}

{% block sidebar_title %}Buyer Hub{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_dashboard' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Overview' item_key='overview' %}

    <!-- Browse Listings -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_browse_listings' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>' label='Browse Listings' item_key='browse' %}

    <!-- Favorites -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_favorites' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path></svg>' label='Favorites' item_key='favorites' %}

    <!-- My Inquiries -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_inquiries' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>' label='My Inquiries' item_key='inquiries' %}
</div>
{% endblock %}

{% block page_title %}My Inquiries{% endblock %}
{% block page_description %}Track your property inquiries and seller responses{% endblock %}

{% block page_actions %}
<a href="{% url 'buyer_browse_listings' %}" class="btn btn-primary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
    </svg>
    Browse Properties
</a>
{% endblock %}

{% block dashboard_content %}
<div class="flex flex-col lg:flex-row gap-6">
    <!-- Filters Sidebar -->
    <div class="lg:w-80 flex-shrink-0">
        <div class="card sticky top-6">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Filter Inquiries</h3>
                <p class="text-sm text-secondary-600">{{ page_obj.paginator.count }} inquir{{ page_obj.paginator.count|pluralize:"y,ies" }} sent</p>
            </div>
            <div class="card-body">
                <form method="get" class="space-y-4">
                    <!-- Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">Status</label>
                        <select name="status" class="form-select" onchange="this.form.submit()">
                            <option value="">All Inquiries</option>
                            <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Awaiting Response</option>
                            <option value="responded" {% if status_filter == 'responded' %}selected{% endif %}>Responded</option>
                        </select>
                    </div>

                    <!-- Clear Filters -->
                    <div class="pt-4 border-t border-secondary-200">
                        <a href="{% url 'buyer_inquiries' %}" class="btn btn-secondary btn-sm w-full">
                            Clear Filters
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Inquiries List -->
    <div class="flex-1">
        {% if page_obj %}
            <div class="space-y-6">
                {% for inquiry in page_obj %}
                    <div class="card hover:shadow-lg transition-shadow duration-200">
                        <div class="card-body">
                            <!-- Inquiry Header -->
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <h3 class="text-lg font-semibold text-secondary-900">
                                            <a href="{% url 'buyer_listing_detail' inquiry.land.id %}" 
                                               class="hover:text-primary-600 transition-colors duration-200">
                                                {{ inquiry.land.title }}
                                            </a>
                                        </h3>
                                        {% if inquiry.seller_response %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                </svg>
                                                Responded
                                            </span>
                                        {% else %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                                </svg>
                                                Pending
                                            </span>
                                        {% endif %}
                                    </div>
                                    <div class="flex items-center text-sm text-secondary-600 space-x-4">
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                            {{ inquiry.land.location }}
                                        </span>
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                            {{ inquiry.land.owner.get_full_name|default:inquiry.land.owner.username }}
                                        </span>
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            Sent {{ inquiry.created_at|timesince }} ago
                                        </span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-lg font-bold text-secondary-900">
                                        ${{ inquiry.land.price|floatformat:0 }}
                                    </span>
                                </div>
                            </div>

                            <!-- Property Image and Details -->
                            <div class="flex items-start space-x-4 mb-4">
                                <div class="w-20 h-16 bg-secondary-200 rounded-lg overflow-hidden flex-shrink-0">
                                    {% if inquiry.land.images.all %}
                                        {% with inquiry.land.images.all|first as primary_image %}
                                            <img src="{{ primary_image.image.url }}" 
                                                 alt="{{ primary_image.alt_text|default:inquiry.land.title }}"
                                                 class="w-full h-full object-cover">
                                        {% endwith %}
                                    {% else %}
                                        <div class="w-full h-full flex items-center justify-center">
                                            <svg class="w-8 h-8 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-4 text-sm text-secondary-600">
                                        <span>{{ inquiry.land.size_acres }} acres</span>
                                        <span>•</span>
                                        <span>{{ inquiry.land.get_property_type_display }}</span>
                                        <span>•</span>
                                        <span>${{ inquiry.land.price|div:inquiry.land.size_acres|floatformat:0 }}/acre</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Inquiry Content -->
                            <div class="bg-secondary-50 rounded-lg p-4 mb-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="text-sm font-medium text-secondary-900">Your Inquiry</h4>
                                    <span class="text-xs text-secondary-500">{{ inquiry.created_at|date:"M j, Y g:i A" }}</span>
                                </div>
                                <div class="mb-2">
                                    <p class="text-sm font-medium text-secondary-800">{{ inquiry.subject }}</p>
                                </div>
                                <p class="text-sm text-secondary-700">{{ inquiry.message }}</p>
                            </div>

                            <!-- Seller Response -->
                            {% if inquiry.seller_response %}
                                <div class="bg-primary-50 rounded-lg p-4 mb-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="text-sm font-medium text-primary-900">Seller Response</h4>
                                        <span class="text-xs text-primary-600">{{ inquiry.response_date|date:"M j, Y g:i A" }}</span>
                                    </div>
                                    <p class="text-sm text-primary-800">{{ inquiry.seller_response }}</p>
                                </div>
                            {% endif %}

                            <!-- Actions -->
                            <div class="flex items-center justify-between pt-4 border-t border-secondary-200">
                                <div class="flex items-center space-x-4">
                                    <a href="{% url 'buyer_listing_detail' inquiry.land.id %}" 
                                       class="text-sm text-primary-600 hover:text-primary-700 font-medium">
                                        View Property Details
                                    </a>
                                    {% if not inquiry.seller_response %}
                                        <span class="text-sm text-secondary-500">Awaiting seller response</span>
                                    {% endif %}
                                </div>
                                <div class="flex items-center space-x-2">
                                    {% if inquiry.seller_response %}
                                        <span class="text-xs text-green-600 font-medium">Response received</span>
                                    {% else %}
                                        <span class="text-xs text-yellow-600 font-medium">Response pending</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <div class="mt-8 flex items-center justify-between">
                    <div class="flex-1 flex justify-between sm:hidden">
                        {% if page_obj.has_previous %}
                            <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
                               class="btn btn-secondary btn-md">Previous</a>
                        {% endif %}
                        {% if page_obj.has_next %}
                            <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" 
                               class="btn btn-secondary btn-md">Next</a>
                        {% endif %}
                    </div>
                    
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-secondary-700">
                                Showing
                                <span class="font-medium">{{ page_obj.start_index }}</span>
                                to
                                <span class="font-medium">{{ page_obj.end_index }}</span>
                                of
                                <span class="font-medium">{{ page_obj.paginator.count }}</span>
                                inquiries
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% if page_obj.has_previous %}
                                    <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50">
                                        <span class="sr-only">Previous</span>
                                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-primary-500 bg-primary-50 text-sm font-medium text-primary-600">
                                            {{ num }}
                                        </span>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}" 
                                           class="relative inline-flex items-center px-4 py-2 border border-secondary-300 bg-white text-sm font-medium text-secondary-700 hover:bg-secondary-50">
                                            {{ num }}
                                        </a>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50">
                                        <span class="sr-only">Next</span>
                                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <!-- No Inquiries -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-secondary-900">No inquiries sent yet</h3>
                <p class="mt-1 text-sm text-secondary-500">Browse properties and send inquiries to sellers to see them here.</p>
                <div class="mt-6">
                    <a href="{% url 'buyer_browse_listings' %}" class="btn btn-primary btn-md">
                        Browse Properties
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}