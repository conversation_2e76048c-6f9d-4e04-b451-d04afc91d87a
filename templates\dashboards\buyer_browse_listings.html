{% extends 'dashboards/base_dashboard.html' %}

{% block dashboard_title %}Browse Listings{% endblock %}
{% block active_nav_item %}browse{% endblock %}

{% block sidebar_icon %}
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
</svg>
{% endblock %}

{% block sidebar_title %}Buyer Hub{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_dashboard' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Overview' item_key='overview' %}

    <!-- Browse Listings -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_browse_listings' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>' label='Browse Listings' item_key='browse' %}

    <!-- Favorites -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_favorites' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path></svg>' label='Favorites' item_key='favorites' %}

    <!-- My Inquiries -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_inquiries' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>' label='My Inquiries' item_key='inquiries' %}
</div>
{% endblock %}

{% block page_title %}Browse Land Listings{% endblock %}
{% block page_description %}Discover available land properties that match your needs{% endblock %}

{% block page_actions %}
<button class="btn btn-secondary btn-md" onclick="window.location.href='{% url 'buyer_favorites' %}'">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
    </svg>
    View Favorites
</button>
{% endblock %}

{% block dashboard_content %}
<div class="flex flex-col lg:flex-row gap-6">
    <!-- Filters Sidebar -->
    <div class="lg:w-80 flex-shrink-0">
        <div class="card sticky top-6">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Filter Properties</h3>
                <p class="text-sm text-secondary-600">{{ total_count }} properties found</p>
            </div>
            <div class="card-body">
                <form hx-get="{% url 'buyer_browse_listings' %}" 
                      hx-target="#listings-grid" 
                      hx-trigger="change, submit"
                      hx-indicator="#loading-indicator"
                      class="space-y-4">
                    
                    <!-- Search -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">Search</label>
                        <input type="text" 
                               name="search" 
                               value="{{ search_query }}"
                               placeholder="Search by title, location, or description..."
                               class="form-input">
                    </div>

                    <!-- Location -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">Location</label>
                        <input type="text" 
                               name="location" 
                               value="{{ location_filter }}"
                               placeholder="City, State, or Region..."
                               class="form-input">
                    </div>

                    <!-- Property Type -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">Property Type</label>
                        <select name="property_type" class="form-select">
                            <option value="">All Types</option>
                            {% for value, label in property_types %}
                                <option value="{{ value }}" {% if property_type_filter == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Price Range -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">Price Range</label>
                        <div class="grid grid-cols-2 gap-2">
                            <input type="number" 
                                   name="min_price" 
                                   value="{{ min_price }}"
                                   placeholder="Min $"
                                   class="form-input">
                            <input type="number" 
                                   name="max_price" 
                                   value="{{ max_price }}"
                                   placeholder="Max $"
                                   class="form-input">
                        </div>
                    </div>

                    <!-- Size Range -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">Size (Acres)</label>
                        <div class="grid grid-cols-2 gap-2">
                            <input type="number" 
                                   name="min_size" 
                                   value="{{ min_size }}"
                                   placeholder="Min acres"
                                   step="0.1"
                                   class="form-input">
                            <input type="number" 
                                   name="max_size" 
                                   value="{{ max_size }}"
                                   placeholder="Max acres"
                                   step="0.1"
                                   class="form-input">
                        </div>
                    </div>

                    <!-- Sort By -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">Sort By</label>
                        <select name="sort" class="form-select">
                            <option value="newest" {% if sort_by == 'newest' %}selected{% endif %}>Newest First</option>
                            <option value="oldest" {% if sort_by == 'oldest' %}selected{% endif %}>Oldest First</option>
                            <option value="price_low" {% if sort_by == 'price_low' %}selected{% endif %}>Price: Low to High</option>
                            <option value="price_high" {% if sort_by == 'price_high' %}selected{% endif %}>Price: High to Low</option>
                            <option value="size_low" {% if sort_by == 'size_low' %}selected{% endif %}>Size: Small to Large</option>
                            <option value="size_high" {% if sort_by == 'size_high' %}selected{% endif %}>Size: Large to Small</option>
                        </select>
                    </div>

                    <!-- Filter Actions -->
                    <div class="flex space-x-2 pt-4 border-t border-secondary-200">
                        <button type="submit" class="btn btn-primary btn-sm flex-1">
                            Apply Filters
                        </button>
                        <button type="button" 
                                onclick="window.location.href='{% url 'buyer_browse_listings' %}'"
                                class="btn btn-secondary btn-sm">
                            Clear
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Listings Grid -->
    <div class="flex-1">
        <!-- Loading Indicator -->
        <div id="loading-indicator" class="htmx-indicator">
            <div class="flex items-center justify-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                <span class="ml-2 text-secondary-600">Loading properties...</span>
            </div>
        </div>

        <!-- Listings Grid Container -->
        <div id="listings-grid">
            {% include 'components/listings_grid.html' %}
        </div>
    </div>
</div>
{% endblock %}