{% extends 'dashboards/base_dashboard.html' %}
{% load form_tags %}

{% block dashboard_title %}Saved Searches{% endblock %}
{% block active_nav_item %}searches{% endblock %}

{% block sidebar_icon %}
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
</svg>
{% endblock %}

{% block sidebar_title %}Buyer Hub{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()" class="space-y-2">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_dashboard' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Dashboard' description='Overview & insights' item_key='overview' %}

    <!-- Browse Listings -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_browse_listings' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>' label='Browse Listings' description='Discover properties' item_key='browse' %}

    <!-- Favorites -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_favorites' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path></svg>' label='Favorites' description='Saved properties' item_key='favorites' %}

    <!-- My Inquiries -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_inquiries' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>' label='My Inquiries' description='Track communications' item_key='inquiries' %}

    <!-- Saved Searches -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_saved_searches' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path></svg>' label='Saved Searches' description='Search alerts' item_key='searches' %}

    <!-- Market Insights -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_market_insights' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Market Insights' description='Trends & analytics' item_key='insights' %}

    <!-- Divider -->
    <div class="my-6">
        <div class="h-px bg-gradient-to-r from-transparent via-slate-600 to-transparent"></div>
    </div>

    <!-- Buying Guide -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_buying_guide' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>' label='Buying Guide' description='Tips & resources' item_key='guide' %}

    <!-- Help & Support -->
    {% include 'components/sidebar_nav_item.html' with href='buyer_support' icon_svg='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>' label='Support' description='Help & documentation' item_key='support' %}
</div>
{% endblock %}

{% block page_title %}Saved Searches{% endblock %}
{% block page_description %}Manage your saved search criteria and alerts{% endblock %}

{% block page_actions %}
<a href="{% url 'buyer_browse_listings' %}" class="btn btn-primary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
    </svg>
    Create New Search
</a>
{% endblock %}

{% block dashboard_content %}
<!-- Filters -->
<div class="bg-white rounded-lg shadow-sm border border-secondary-200 p-6 mb-6">
    <form method="get" class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
            <label class="block text-sm font-medium text-secondary-700 mb-2">Filter by Status</label>
            <select name="status" class="form-select" onchange="this.form.submit()">
                <option value="">All Searches</option>
                <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active</option>
                <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>Inactive</option>
            </select>
        </div>
        <div class="flex items-end">
            <span class="text-sm text-secondary-600">{{ total_count }} search{{ total_count|pluralize:"es" }} found</span>
        </div>
    </form>
</div>

<!-- Saved Searches List -->
<div class="bg-white rounded-lg shadow-sm border border-secondary-200 overflow-hidden">
    {% if page_obj %}
        <div class="divide-y divide-secondary-200">
            {% for search in page_obj %}
                <div class="p-6 hover:bg-secondary-50 transition-colors duration-200">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h3 class="text-lg font-semibold text-secondary-900">{{ search.name }}</h3>
                                {% if search.is_active %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Inactive
                                    </span>
                                {% endif %}
                                {% if search.email_alerts %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                        </svg>
                                        Alerts
                                    </span>
                                {% endif %}
                            </div>
                            
                            <!-- Search Criteria -->
                            <div class="space-y-2 mb-4">
                                {% if search.search_query %}
                                    <div class="flex items-center text-sm text-secondary-600">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                        Keywords: <span class="font-medium ml-1">{{ search.search_query }}</span>
                                    </div>
                                {% endif %}
                                {% if search.location_filter %}
                                    <div class="flex items-center text-sm text-secondary-600">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        Location: <span class="font-medium ml-1">{{ search.location_filter }}</span>
                                    </div>
                                {% endif %}
                                {% if search.property_type_filter %}
                                    <div class="flex items-center text-sm text-secondary-600">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                        Type: <span class="font-medium ml-1">{{ search.get_property_type_filter_display }}</span>
                                    </div>
                                {% endif %}
                                {% if search.min_price or search.max_price %}
                                    <div class="flex items-center text-sm text-secondary-600">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                        Price: <span class="font-medium ml-1">
                                            {% if search.min_price and search.max_price %}
                                                ${{ search.min_price|floatformat:0 }} - ${{ search.max_price|floatformat:0 }}
                                            {% elif search.min_price %}
                                                ${{ search.min_price|floatformat:0 }}+
                                            {% elif search.max_price %}
                                                Up to ${{ search.max_price|floatformat:0 }}
                                            {% endif %}
                                        </span>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- Matching Properties Count -->
                            <div class="flex items-center space-x-4 text-sm text-secondary-500">
                                <span>{{ search.matching_count }} matching propert{{ search.matching_count|pluralize:"y,ies" }}</span>
                                <span>•</span>
                                <span>Created {{ search.created_at|timesince }} ago</span>
                                {% if search.last_alert_sent %}
                                    <span>•</span>
                                    <span>Last alert {{ search.last_alert_sent|timesince }} ago</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex items-center space-x-2 ml-4">
                            <a href="{{ search.get_search_url }}" 
                               class="btn btn-secondary btn-sm">
                                View Results
                            </a>
                            <button class="btn btn-outline btn-sm">
                                Edit
                            </button>
                            <button class="btn btn-outline btn-sm text-red-600 hover:text-red-700">
                                Delete
                            </button>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
            <div class="px-6 py-4 border-t border-secondary-200 bg-secondary-50">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-secondary-700">
                        Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
                    </div>
                    <div class="flex space-x-2">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
                               class="btn btn-outline btn-sm">Previous</a>
                        {% endif %}
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
                               class="btn btn-outline btn-sm">Next</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endif %}
    {% else %}
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-secondary-900">No saved searches</h3>
            <p class="mt-1 text-sm text-secondary-500">Start by browsing properties and saving your search criteria.</p>
            <div class="mt-6">
                <a href="{% url 'buyer_browse_listings' %}" class="btn btn-primary btn-md">
                    Browse Properties
                </a>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
