<!-- Listings Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
    {% for listing in page_obj %}
        <div class="card hover:shadow-lg transition-all duration-200 group">
            <!-- Image Container -->
            <div class="relative overflow-hidden rounded-t-lg">
                {% if listing.images.all %}
                    {% with listing.images.all|first as primary_image %}
                        <img src="{{ primary_image.image.url }}" 
                             alt="{{ primary_image.alt_text|default:listing.title }}"
                             class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200">
                    {% endwith %}
                {% else %}
                    <div class="w-full h-48 bg-secondary-200 flex items-center justify-center">
                        <svg class="w-12 h-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                {% endif %}
                
                <!-- Property Type Badge -->
                <div class="absolute top-3 left-3">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white bg-opacity-90 text-secondary-800">
                        {{ listing.get_property_type_display }}
                    </span>
                </div>
                
                <!-- Favorite Button -->
                <div class="absolute top-3 right-3">
                    {% include 'components/favorite_button.html' with listing=listing is_favorited=listing.id|add:0 in user_favorites %}
                </div>
            </div>
            
            <!-- Card Content -->
            <div class="card-body">
                <!-- Title and Location -->
                <div class="mb-3">
                    <h3 class="text-lg font-semibold text-secondary-900 mb-1 line-clamp-2">
                        <a href="{% url 'buyer_listing_detail' listing.id %}" 
                           class="hover:text-primary-600 transition-colors duration-200">
                            {{ listing.title }}
                        </a>
                    </h3>
                    <p class="text-sm text-secondary-600 flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        {{ listing.location }}
                    </p>
                </div>
                
                <!-- Property Details -->
                <div class="flex items-center justify-between text-sm text-secondary-600 mb-4">
                    <div class="flex items-center space-x-4">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                            </svg>
                            {{ listing.size_acres }} acres
                        </span>
                    </div>
                    <span class="text-xs text-secondary-500">
                        Listed {{ listing.created_at|timesince }} ago
                    </span>
                </div>
                
                <!-- Description -->
                <p class="text-sm text-secondary-600 mb-4 line-clamp-2">
                    {{ listing.description|truncatewords:20 }}
                </p>
                
                <!-- Price and Actions -->
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-2xl font-bold text-secondary-900">
                            ${{ listing.price|floatformat:0 }}
                        </p>
                        <p class="text-xs text-secondary-500">
                            ${{ listing.price|div:listing.size_acres|floatformat:0 }}/acre
                        </p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <a href="{% url 'buyer_listing_detail' listing.id %}" 
                           class="btn btn-primary btn-sm">
                            View Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% empty %}
        <!-- No Results -->
        <div class="col-span-full">
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-secondary-900">No properties found</h3>
                <p class="mt-1 text-sm text-secondary-500">Try adjusting your search filters to find more properties.</p>
                <div class="mt-6">
                    <button onclick="window.location.href='{% url 'buyer_browse_listings' %}'" 
                            class="btn btn-primary btn-md">
                        Clear All Filters
                    </button>
                </div>
            </div>
        </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if page_obj.has_other_pages %}
    <div class="mt-8 flex items-center justify-between">
        <div class="flex-1 flex justify-between sm:hidden">
            {% if page_obj.has_previous %}
                <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
                   class="btn btn-secondary btn-md">Previous</a>
            {% endif %}
            {% if page_obj.has_next %}
                <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" 
                   class="btn btn-secondary btn-md">Next</a>
            {% endif %}
        </div>
        
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-secondary-700">
                    Showing
                    <span class="font-medium">{{ page_obj.start_index }}</span>
                    to
                    <span class="font-medium">{{ page_obj.end_index }}</span>
                    of
                    <span class="font-medium">{{ page_obj.paginator.count }}</span>
                    results
                </p>
            </div>
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    {% if page_obj.has_previous %}
                        <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50">
                            <span class="sr-only">Previous</span>
                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="relative inline-flex items-center px-4 py-2 border border-primary-500 bg-primary-50 text-sm font-medium text-primary-600">
                                {{ num }}
                            </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}" 
                               class="relative inline-flex items-center px-4 py-2 border border-secondary-300 bg-white text-sm font-medium text-secondary-700 hover:bg-secondary-50">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50">
                            <span class="sr-only">Next</span>
                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
{% endif %}